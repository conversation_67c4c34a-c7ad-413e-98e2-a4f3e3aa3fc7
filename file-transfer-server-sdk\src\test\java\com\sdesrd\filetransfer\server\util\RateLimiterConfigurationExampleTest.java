package com.sdesrd.filetransfer.server.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 限流器配置示例测试
 * 演示 cleanupIntervalMs 和 idleTimeoutMs 两个配置项的功能区别
 */
@Slf4j
@DisplayName("限流器配置示例测试")
class RateLimiterConfigurationExampleTest {
    
    @AfterEach
    void tearDown() {
        RateLimitUtils.stopCleanupScheduler();
        RateLimitUtils.clearAllRateLimiters();
    }
    
    @Test
    @DisplayName("示例1：标准配置 - 清理间隔5分钟，空闲超时10分钟")
    void exampleStandardConfiguration() throws InterruptedException {
        log.info("=== 示例1：标准配置演示 ===");
        log.info("配置：清理间隔5分钟，空闲超时10分钟");
        log.info("效果：限流器空闲10分钟后，在下个5分钟检查点被清理");
        
        // 使用标准配置（缩短时间用于测试）
        long cleanupInterval = 1000L; // 1秒（模拟5分钟）
        long idleTimeout = 2000L;     // 2秒（模拟10分钟）
        
        RateLimitUtils.configure(cleanupInterval, idleTimeout);
        
        // 创建限流器
        RateLimitUtils.applyRateLimit("user1_upload", 1024L, 100L);
        log.info("时间点0: 创建限流器，当前数量: {}", RateLimitUtils.getRateLimiterCount());
        assertEquals(1, RateLimitUtils.getRateLimiterCount());
        
        // 等待1秒（一个清理间隔）
        Thread.sleep(1100);
        log.info("时间点1秒: 第1次清理检查，限流器空闲1秒 < 2秒，应保留");
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "限流器空闲时间未超过阈值，应该保留");
        
        // 再等待1秒（总共2秒，达到空闲超时）
        Thread.sleep(1000);
        log.info("时间点2秒: 第2次清理检查，限流器空闲2秒 = 2秒，应清理");
        assertEquals(0, RateLimitUtils.getRateLimiterCount(), "限流器空闲时间达到阈值，应该被清理");
        
        log.info("✓ 标准配置演示完成");
    }
    
    @Test
    @DisplayName("示例2：快速清理配置 - 清理间隔1分钟，空闲超时3分钟")
    void exampleFastCleanupConfiguration() throws InterruptedException {
        log.info("=== 示例2：快速清理配置演示 ===");
        log.info("配置：清理间隔1分钟，空闲超时3分钟");
        log.info("适用场景：内存敏感环境，需要快速释放资源");
        
        // 快速清理配置（缩短时间用于测试）
        long cleanupInterval = 500L;  // 0.5秒（模拟1分钟）
        long idleTimeout = 1500L;     // 1.5秒（模拟3分钟）
        
        RateLimitUtils.configure(cleanupInterval, idleTimeout);
        
        // 创建多个限流器
        RateLimitUtils.applyRateLimit("user1_upload", 1024L, 100L);
        RateLimitUtils.applyRateLimit("user2_download", 1024L, 100L);
        log.info("时间点0: 创建2个限流器，当前数量: {}", RateLimitUtils.getRateLimiterCount());
        assertEquals(2, RateLimitUtils.getRateLimiterCount());
        
        // 等待0.5秒（一个清理间隔）
        Thread.sleep(600);
        log.info("时间点0.5秒: 第1次清理检查，限流器空闲0.5秒 < 1.5秒，应保留");
        assertEquals(2, RateLimitUtils.getRateLimiterCount());
        
        // 等待1秒（一个清理间隔）
        Thread.sleep(500);
        log.info("时间点1秒: 第2次清理检查，限流器空闲1秒 < 1.5秒，应保留");
        assertEquals(2, RateLimitUtils.getRateLimiterCount());
        
        // 等待0.5秒（总共1.5秒，达到空闲超时）
        Thread.sleep(600);
        log.info("时间点1.5秒: 第3次清理检查，限流器空闲1.5秒 = 1.5秒，应清理");
        assertEquals(0, RateLimitUtils.getRateLimiterCount(), "所有限流器都应该被清理");
        
        log.info("✓ 快速清理配置演示完成");
    }
    
    @Test
    @DisplayName("示例3：保守清理配置 - 清理间隔10分钟，空闲超时30分钟")
    void exampleConservativeConfiguration() throws InterruptedException {
        log.info("=== 示例3：保守清理配置演示 ===");
        log.info("配置：清理间隔10分钟，空闲超时30分钟");
        log.info("适用场景：高频访问环境，最大化限流器重用");
        
        // 保守清理配置（缩短时间用于测试）
        long cleanupInterval = 2000L; // 2秒（模拟10分钟）
        long idleTimeout = 6000L;     // 6秒（模拟30分钟）
        
        RateLimitUtils.configure(cleanupInterval, idleTimeout);
        
        // 创建限流器
        RateLimitUtils.applyRateLimit("frequent_user", 1024L, 100L);
        log.info("时间点0: 创建限流器，当前数量: {}", RateLimitUtils.getRateLimiterCount());
        assertEquals(1, RateLimitUtils.getRateLimiterCount());
        
        // 等待2秒（一个清理间隔）
        Thread.sleep(2100);
        log.info("时间点2秒: 第1次清理检查，限流器空闲2秒 < 6秒，应保留");
        assertEquals(1, RateLimitUtils.getRateLimiterCount());
        
        // 等待2秒（总共4秒）
        Thread.sleep(2000);
        log.info("时间点4秒: 第2次清理检查，限流器空闲4秒 < 6秒，应保留");
        assertEquals(1, RateLimitUtils.getRateLimiterCount());
        
        // 模拟用户重新使用限流器
        RateLimitUtils.applyRateLimit("frequent_user", 1024L, 100L);
        log.info("时间点4秒: 用户重新使用限流器，重置空闲时间");
        
        // 等待2秒（第3次清理检查）
        Thread.sleep(2100);
        log.info("时间点6秒: 第3次清理检查，限流器刚被使用，应保留");
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "限流器被重新使用，应该继续保留");
        
        log.info("✓ 保守清理配置演示完成 - 限流器得到有效重用");
    }
    
    @Test
    @DisplayName("示例4：错误配置演示 - 清理间隔大于空闲超时")
    void exampleIncorrectConfiguration() {
        log.info("=== 示例4：错误配置演示 ===");
        log.info("错误配置：清理间隔10分钟，空闲超时5分钟");
        log.info("问题：限流器空闲5分钟就应该清理，但要等10分钟才检查一次");
        
        // 错误配置
        long cleanupInterval = 2000L; // 2秒（模拟10分钟）
        long idleTimeout = 1000L;     // 1秒（模拟5分钟）
        
        log.warn("配置警告：清理间隔({})大于空闲超时({})，这会导致清理延迟", cleanupInterval, idleTimeout);
        log.warn("建议：空闲超时应该是清理间隔的2-3倍");
        
        RateLimitUtils.configure(cleanupInterval, idleTimeout);
        
        String configInfo = RateLimitUtils.getConfigurationInfo();
        log.info("当前配置: {}", configInfo);
        
        // 虽然配置不合理，但系统仍能工作
        assertTrue(RateLimitUtils.isCleanupRunning(), "即使配置不合理，清理任务仍应启动");
        
        log.info("✓ 错误配置演示完成 - 系统仍可工作但效率不佳");
    }
    
    @Test
    @DisplayName("配置建议总结")
    void configurationRecommendations() {
        log.info("=== 限流器配置建议总结 ===");
        
        log.info("1. 基本原则：");
        log.info("   - 空闲超时 > 清理间隔（建议2-3倍关系）");
        log.info("   - 清理间隔不要太短（避免频繁检查）");
        log.info("   - 空闲超时不要太长（避免内存占用）");
        
        log.info("2. 场景配置：");
        log.info("   - 高频访问：清理间隔10分钟，空闲超时30分钟");
        log.info("   - 标准场景：清理间隔5分钟，空闲超时10分钟");
        log.info("   - 内存敏感：清理间隔2分钟，空闲超时5分钟");
        
        log.info("3. 配置验证：");
        log.info("   - 检查日志中的清理统计信息");
        log.info("   - 监控限流器数量变化");
        log.info("   - 观察内存使用情况");
        
        log.info("✓ 配置建议总结完成");
    }
}
