package com.sdesrd.filetransfer.client.util;

import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

import lombok.extern.slf4j.Slf4j;

/**
 * 重试管理器
 * 支持指数退避和抖动的重试策略
 */
@Slf4j
public class RetryManager {
    
    /** 默认重试次数 */
    private static final int DEFAULT_MAX_RETRIES = 3;
    
    /** 默认基础延迟时间（毫秒） */
    private static final long DEFAULT_BASE_DELAY_MS = 1000L;
    
    /** 默认最大延迟时间（毫秒） */
    private static final long DEFAULT_MAX_DELAY_MS = 30000L;
    
    /** 默认退避倍数 */
    private static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0;
    
    /** 默认抖动因子 */
    private static final double DEFAULT_JITTER_FACTOR = 0.1;
    
    /**
     * 重试策略配置
     */
    public static class RetryPolicy {
        private final int maxRetries;
        private final long baseDelayMs;
        private final long maxDelayMs;
        private final double backoffMultiplier;
        private final double jitterFactor;
        private final Class<? extends Throwable>[] retryableExceptions;
        
        @SafeVarargs
        public RetryPolicy(int maxRetries, long baseDelayMs, long maxDelayMs, 
                          double backoffMultiplier, double jitterFactor,
                          Class<? extends Throwable>... retryableExceptions) {
            this.maxRetries = maxRetries;
            this.baseDelayMs = baseDelayMs;
            this.maxDelayMs = maxDelayMs;
            this.backoffMultiplier = backoffMultiplier;
            this.jitterFactor = jitterFactor;
            this.retryableExceptions = retryableExceptions;
        }
        
        // Getters
        public int getMaxRetries() { return maxRetries; }
        public long getBaseDelayMs() { return baseDelayMs; }
        public long getMaxDelayMs() { return maxDelayMs; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public double getJitterFactor() { return jitterFactor; }
        public Class<? extends Throwable>[] getRetryableExceptions() { return retryableExceptions; }
    }
    
    /**
     * 执行带重试的操作
     * 
     * @param operation 要执行的操作
     * @param policy 重试策略
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithRetry(Supplier<T> operation, RetryPolicy policy, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 0; attempt <= policy.getMaxRetries(); attempt++) {
            try {
                if (attempt > 0) {
                    log.debug("重试操作 - 操作: {}, 尝试次数: {}/{}", operationName, attempt, policy.getMaxRetries());
                }
                
                return operation.get();
                
            } catch (Exception e) {
                lastException = e;
                
                // 检查是否是可重试的异常
                if (!isRetryableException(e, policy)) {
                    log.warn("不可重试的异常 - 操作: {}, 异常: {}", operationName, e.getMessage());
                    throw e;
                }
                
                // 如果已经是最后一次尝试，不再重试
                if (attempt >= policy.getMaxRetries()) {
                    log.error("重试次数已达上限 - 操作: {}, 尝试次数: {}, 最后异常: {}", 
                            operationName, attempt + 1, e.getMessage());
                    break;
                }
                
                // 计算延迟时间并等待
                long delayMs = calculateDelay(attempt, policy);
                log.warn("操作失败，将在{}ms后重试 - 操作: {}, 尝试次数: {}/{}, 异常: {}", 
                        delayMs, operationName, attempt + 1, policy.getMaxRetries() + 1, e.getMessage());
                
                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
        
        throw new RuntimeException("重试失败 - 操作: " + operationName, lastException);
    }
    
    /**
     * 执行带重试的操作（使用默认策略）
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName) throws Exception {
        RetryPolicy defaultPolicy = new RetryPolicyBuilder()
                .maxRetries(DEFAULT_MAX_RETRIES)
                .baseDelay(DEFAULT_BASE_DELAY_MS)
                .maxDelay(DEFAULT_MAX_DELAY_MS)
                .backoffMultiplier(DEFAULT_BACKOFF_MULTIPLIER)
                .jitterFactor(DEFAULT_JITTER_FACTOR)
                .build();
        return executeWithRetry(operation, defaultPolicy, operationName);
    }
    
    /**
     * 执行带重试的操作（无返回值）
     */
    public static void executeWithRetry(Runnable operation, RetryPolicy policy, String operationName) throws Exception {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, policy, operationName);
    }
    
    /**
     * 计算延迟时间（指数退避 + 抖动）
     */
    private static long calculateDelay(int attempt, RetryPolicy policy) {
        // 指数退避
        double delay = policy.getBaseDelayMs() * Math.pow(policy.getBackoffMultiplier(), attempt);
        
        // 限制最大延迟
        delay = Math.min(delay, policy.getMaxDelayMs());
        
        // 添加抖动
        double jitter = delay * policy.getJitterFactor() * (ThreadLocalRandom.current().nextDouble() * 2 - 1);
        delay += jitter;
        
        return Math.max(0, (long) delay);
    }
    
    /**
     * 检查异常是否可重试
     */
    private static boolean isRetryableException(Exception e, RetryPolicy policy) {
        // 如果没有指定可重试异常，默认所有异常都可重试
        if (policy.getRetryableExceptions() == null || policy.getRetryableExceptions().length == 0) {
            return true;
        }
        
        // 检查异常类型是否匹配
        for (Class<? extends Throwable> retryableType : policy.getRetryableExceptions()) {
            if (retryableType.isAssignableFrom(e.getClass())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 基于ClientConfig创建文件级重传策略
     * 用于整个文件的上传/下载操作
     * 
     * 传输逻辑说明：
     * 1. 重传次数：直接使用 config.retryCount（默认3次）
     * 2. 基础延迟：直接使用 config.retryIntervalMs（默认1000ms）
     * 3. 最大延迟：设置为基础延迟的10倍，避免过长等待时间（默认10秒）
     * 4. 指数退避：使用温和的1.5倍递增，平衡重传速度和服务器压力
     * 5. 抖动因子：20%的随机波动，避免多个客户端同时重传造成雷群效应
     * 6. 适用场景：整个文件传输流程失败时的重传，包括网络断连、服务器错误、文件校验失败等
     * 
     * @param config 客户端配置
     * @return 文件级重传策略
     */
    public static RetryPolicy createFileRetryPolicy(com.sdesrd.filetransfer.client.config.ClientConfig config) {
        return new RetryPolicyBuilder()
                .maxRetries(config.getEffectiveFileRetryCount())
                .baseDelay(config.getEffectiveFileRetryIntervalMs())
                .maxDelay(config.getEffectiveFileRetryIntervalMs() * 10L) // 最大延迟为基础延迟的10倍
                .backoffMultiplier(1.5) // 使用温和的指数退避
                .jitterFactor(0.2) // 添加20%的抖动避免雷群效应
                .build();
    }
    
    /**
     * 基于ClientConfig创建chunk级重传策略
     * 用于单个分块的上传/下载操作，使用更快的重传策略
     * 
     * 传输逻辑说明：
     * 1. 重传次数：使用 max(1, config.retryCount / 2) 计算（默认配置下为1次重传）
     * 2. 基础延迟：使用 max(100, config.retryIntervalMs / 5) 计算（默认配置下为200ms）
     * 3. 最大延迟：设置为基础延迟的5倍，确保快速响应（默认1秒）
     * 4. 指数退避：使用快速的2.0倍递增，快速识别持续失败的分块
     * 5. 抖动因子：10%的随机波动，减少但不完全避免雷群效应
     * 6. 适用场景：单个分块传输失败的快速重传，如暂时性网络抖动、单个分块数据错误等
     * 7. 设计理念：快速失败，避免在单个分块上耗费过多时间，依赖文件级重传处理持续性问题
     * 
     * @param config 客户端配置
     * @return chunk级重传策略
     */
    public static RetryPolicy createChunkRetryPolicy(com.sdesrd.filetransfer.client.config.ClientConfig config) {
        return new RetryPolicyBuilder()
                .maxRetries(config.getEffectiveChunkRetryCount())
                .baseDelay(config.getEffectiveChunkRetryIntervalMs())
                .maxDelay(config.getEffectiveChunkRetryIntervalMs() * 5L) // chunk最大延迟为基础延迟的5倍
                .backoffMultiplier(2.0) // 使用快速的指数退避
                .jitterFactor(0.1) // 添加10%的抖动
                .build();
    }
    
    /**
     * 重试策略构建器
     */
    public static class RetryPolicyBuilder {
        private int maxRetries = DEFAULT_MAX_RETRIES;
        private long baseDelayMs = DEFAULT_BASE_DELAY_MS;
        private long maxDelayMs = DEFAULT_MAX_DELAY_MS;
        private double backoffMultiplier = DEFAULT_BACKOFF_MULTIPLIER;
        private double jitterFactor = DEFAULT_JITTER_FACTOR;
        private Class<? extends Throwable>[] retryableExceptions;
        
        public RetryPolicyBuilder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        public RetryPolicyBuilder baseDelay(long baseDelayMs) {
            this.baseDelayMs = baseDelayMs;
            return this;
        }
        
        public RetryPolicyBuilder maxDelay(long maxDelayMs) {
            this.maxDelayMs = maxDelayMs;
            return this;
        }
        
        public RetryPolicyBuilder backoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
            return this;
        }
        
        public RetryPolicyBuilder jitterFactor(double jitterFactor) {
            this.jitterFactor = jitterFactor;
            return this;
        }
        
        @SafeVarargs
        public final RetryPolicyBuilder retryOn(Class<? extends Throwable>... exceptions) {
            this.retryableExceptions = exceptions;
            return this;
        }
        
        public RetryPolicy build() {
            return new RetryPolicy(maxRetries, baseDelayMs, maxDelayMs, 
                    backoffMultiplier, jitterFactor, retryableExceptions);
        }
    }
}
