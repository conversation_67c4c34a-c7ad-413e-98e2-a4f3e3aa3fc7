package com.sdesrd.filetransfer.client.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 客户端配置参数测试
 * 验证客户端新增的配置项能够正确工作
 */
@Slf4j
@DisplayName("客户端配置参数测试")
class ClientConfigurationTest {
    
    private ClientConfig config;
    
    @BeforeEach
    void setUp() {
        config = new ClientConfig();
        log.info("客户端配置参数测试 - 开始验证新增配置项");
    }
    
    @Test
    @DisplayName("验证最大并发下载数配置")
    void testMaxConcurrentDownloadsConfiguration() {
        log.info("测试最大并发下载数配置 - 默认值应该为8");
        
        assertEquals(8, config.getMaxConcurrentDownloads(), 
            "最大并发下载数默认值应该为8");
        
        // 测试设置自定义值
        config.setMaxConcurrentDownloads(16);
        assertEquals(16, config.getMaxConcurrentDownloads(), 
            "应该能够设置自定义的最大并发下载数");
        
        log.info("✓ 最大并发下载数配置验证通过: {}", config.getMaxConcurrentDownloads());
    }
    
    @Test
    @DisplayName("验证进度更新间隔配置")
    void testProgressUpdateIntervalConfiguration() {
        log.info("测试进度更新间隔配置 - 默认值应该为500ms");
        
        assertEquals(500L, config.getProgressUpdateIntervalMs(), 
            "进度更新间隔默认值应该为500ms");
        
        // 测试设置自定义值
        config.setProgressUpdateIntervalMs(1000L);
        assertEquals(1000L, config.getProgressUpdateIntervalMs(), 
            "应该能够设置自定义的进度更新间隔");
        
        log.info("✓ 进度更新间隔配置验证通过: {}ms", config.getProgressUpdateIntervalMs());
    }
    
    @Test
    @DisplayName("验证配置项默认值正确")
    void testDefaultConfigurationValues() {
        log.info("测试客户端配置项默认值");
        
        ClientConfig defaultConfig = new ClientConfig();
        
        // 验证新增配置项的默认值
        assertEquals(8, defaultConfig.getMaxConcurrentDownloads(), 
            "最大并发下载数默认值应该为8");
        assertEquals(500L, defaultConfig.getProgressUpdateIntervalMs(), 
            "进度更新间隔默认值应该为500ms");
        
        // 验证原有配置项的默认值未受影响
        assertEquals(3, defaultConfig.getMaxConcurrentTransfers(), 
            "最大并发传输数默认值应该为3");
        assertEquals(2 * 1024 * 1024L, defaultConfig.getChunkSize(), 
            "分块大小默认值应该为2MB");
        assertEquals(30, defaultConfig.getConnectTimeoutSeconds(), 
            "连接超时默认值应该为30秒");
        assertEquals(60, defaultConfig.getReadTimeoutSeconds(), 
            "读取超时默认值应该为60秒");
        assertEquals(60, defaultConfig.getWriteTimeoutSeconds(), 
            "写入超时默认值应该为60秒");
        
        log.info("✓ 所有客户端配置项默认值验证通过");
    }
    
    @Test
    @DisplayName("验证配置项向后兼容性")
    void testBackwardCompatibility() {
        log.info("测试客户端配置项向后兼容性 - 确保新增配置不影响现有功能");
        
        ClientConfig config = new ClientConfig();
        
        // 验证原有配置项仍然正常工作
        assertNotNull(config.getServerAddr(), "服务器地址应该有默认值");
        assertEquals(49011, config.getServerPort(), "服务器端口应该保持默认值");
        assertNotNull(config.getContextPath(), "上下文路径应该有默认值");
        assertNotNull(config.getUsername(), "用户名应该有默认值");
        assertNotNull(config.getSecretKey(), "密钥应该有默认值");
        
        log.info("✓ 客户端向后兼容性验证通过 - 现有配置项未受影响");
    }
    
    @Test
    @DisplayName("验证配置项合理性检查")
    void testConfigurationValidation() {
        log.info("测试配置项合理性检查");
        
        ClientConfig config = new ClientConfig();
        
        // 测试边界值
        config.setMaxConcurrentDownloads(1);
        assertEquals(1, config.getMaxConcurrentDownloads(), 
            "应该允许设置最小并发下载数为1");
        
        config.setMaxConcurrentDownloads(32);
        assertEquals(32, config.getMaxConcurrentDownloads(), 
            "应该允许设置较大的并发下载数");
        
        config.setProgressUpdateIntervalMs(100L);
        assertEquals(100L, config.getProgressUpdateIntervalMs(), 
            "应该允许设置较短的进度更新间隔");
        
        config.setProgressUpdateIntervalMs(5000L);
        assertEquals(5000L, config.getProgressUpdateIntervalMs(), 
            "应该允许设置较长的进度更新间隔");
        
        log.info("✓ 配置项合理性检查通过");
    }
    
    @Test
    @DisplayName("验证配置项与现有配置的协调性")
    void testConfigurationCoordination() {
        log.info("测试新增配置项与现有配置的协调性");
        
        ClientConfig config = new ClientConfig();
        
        // 验证最大并发下载数与最大并发传输数的关系
        assertTrue(config.getMaxConcurrentDownloads() >= config.getMaxConcurrentTransfers(),
            "最大并发下载数应该大于等于最大并发传输数");
        
        // 验证进度更新间隔的合理性
        assertTrue(config.getProgressUpdateIntervalMs() > 0,
            "进度更新间隔应该大于0");
        assertTrue(config.getProgressUpdateIntervalMs() < config.getReadTimeoutSeconds() * 1000,
            "进度更新间隔应该小于读取超时时间");
        
        log.info("✓ 配置项协调性验证通过");
    }
    
    @Test
    @DisplayName("验证配置基本功能")
    void testBasicConfiguration() {
        log.info("测试配置基本功能");

        ClientConfig config = new ClientConfig();

        // 验证新增配置项有正确的默认值
        assertEquals(8, config.getMaxConcurrentDownloads(),
            "最大并发下载数默认值应该为8");
        assertEquals(500L, config.getProgressUpdateIntervalMs(),
            "进度更新间隔默认值应该为500ms");

        // 验证可以设置新值
        config.setMaxConcurrentDownloads(16);
        config.setProgressUpdateIntervalMs(1000L);

        assertEquals(16, config.getMaxConcurrentDownloads());
        assertEquals(1000L, config.getProgressUpdateIntervalMs());

        log.info("✓ 配置基本功能验证通过");
    }
}
