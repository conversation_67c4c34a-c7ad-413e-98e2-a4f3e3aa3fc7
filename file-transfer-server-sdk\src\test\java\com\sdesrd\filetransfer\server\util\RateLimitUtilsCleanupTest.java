package com.sdesrd.filetransfer.server.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 限流器定时清理功能测试
 * 验证cleanupIntervalMs配置项的实际使用和定时清理机制
 */
@Slf4j
@DisplayName("限流器定时清理功能测试")
class RateLimitUtilsCleanupTest {
    
    private static final String TEST_USER = "testUser";
    private static final long TEST_RATE_LIMIT = 1024 * 1024L; // 1MB/s
    private static final long TEST_DATA_SIZE = 1024L; // 1KB
    
    @BeforeEach
    void setUp() {
        log.info("测试准备 - 清理所有限流器并停止清理任务");
        RateLimitUtils.stopCleanupScheduler();
        RateLimitUtils.clearAllRateLimiters();
    }
    
    @AfterEach
    void tearDown() {
        log.info("测试清理 - 停止清理任务并清理所有限流器");
        RateLimitUtils.stopCleanupScheduler();
        RateLimitUtils.clearAllRateLimiters();
    }
    
    @Test
    @DisplayName("验证cleanupIntervalMs配置项被正确使用")
    void testCleanupIntervalConfiguration() {
        log.info("测试cleanupIntervalMs配置项使用");
        
        // 配置较短的清理间隔用于测试
        long testCleanupInterval = 1000L; // 1秒
        long testIdleTimeout = 500L; // 0.5秒
        
        // 验证清理任务初始状态
        assertFalse(RateLimitUtils.isCleanupRunning(), "清理任务初始应该是停止状态");
        
        // 配置限流器参数，这应该启动清理任务
        RateLimitUtils.configure(testCleanupInterval, testIdleTimeout);
        
        // 验证清理任务已启动
        assertTrue(RateLimitUtils.isCleanupRunning(), "配置后清理任务应该启动");
        
        // 验证配置信息
        String configInfo = RateLimitUtils.getConfigurationInfo();
        log.info("配置信息: {}", configInfo);
        assertTrue(configInfo.contains("1000ms"), "配置信息应该包含正确的清理间隔");
        assertTrue(configInfo.contains("500ms"), "配置信息应该包含正确的空闲超时");
        assertTrue(configInfo.contains("运行中"), "配置信息应该显示清理任务运行中");
        
        log.info("✓ cleanupIntervalMs配置项使用验证通过");
    }
    
    @Test
    @DisplayName("验证定时清理机制工作正常")
    void testScheduledCleanupMechanism() throws InterruptedException {
        log.info("测试定时清理机制");
        
        // 配置较短的清理间隔和空闲超时用于测试
        long testCleanupInterval = 500L; // 0.5秒
        long testIdleTimeout = 200L; // 0.2秒
        
        RateLimitUtils.configure(testCleanupInterval, testIdleTimeout);
        
        // 创建一些限流器
        String uploadKey = TEST_USER + "_upload";
        String downloadKey = TEST_USER + "_download";
        
        RateLimitUtils.applyRateLimit(uploadKey, TEST_RATE_LIMIT, TEST_DATA_SIZE);
        RateLimitUtils.applyRateLimit(downloadKey, TEST_RATE_LIMIT, TEST_DATA_SIZE);
        
        // 验证限流器已创建
        assertEquals(2, RateLimitUtils.getRateLimiterCount(), "应该创建2个限流器");
        
        // 等待超过空闲超时时间，让限流器变为空闲状态
        Thread.sleep(testIdleTimeout + 100); // 等待300ms
        
        // 再等待一个清理间隔，让定时清理任务执行
        Thread.sleep(testCleanupInterval + 200); // 等待700ms
        
        // 验证空闲的限流器已被清理
        int remainingCount = RateLimitUtils.getRateLimiterCount();
        log.info("清理后剩余限流器数量: {}", remainingCount);
        assertEquals(0, remainingCount, "空闲的限流器应该被自动清理");
        
        log.info("✓ 定时清理机制验证通过");
    }
    
    @Test
    @DisplayName("验证清理任务启停功能")
    void testCleanupSchedulerStartStop() {
        log.info("测试清理任务启停功能");
        
        // 初始状态应该是停止的
        assertFalse(RateLimitUtils.isCleanupRunning(), "初始状态清理任务应该是停止的");
        
        // 配置并启动清理任务
        RateLimitUtils.configure(1000L, 500L);
        assertTrue(RateLimitUtils.isCleanupRunning(), "配置后清理任务应该启动");
        
        // 停止清理任务
        RateLimitUtils.stopCleanupScheduler();
        assertFalse(RateLimitUtils.isCleanupRunning(), "停止后清理任务应该是停止状态");
        
        // 重新配置应该重新启动清理任务
        RateLimitUtils.configure(2000L, 1000L);
        assertTrue(RateLimitUtils.isCleanupRunning(), "重新配置后清理任务应该重新启动");
        
        log.info("✓ 清理任务启停功能验证通过");
    }
    
    @Test
    @DisplayName("验证统计信息包含清理任务状态")
    void testStatisticsIncludeCleanupStatus() {
        log.info("测试统计信息包含清理任务状态");
        
        // 停止状态的统计信息
        String statsWhenStopped = RateLimitUtils.getRateLimiterStats();
        log.info("停止状态统计: {}", statsWhenStopped);
        assertTrue(statsWhenStopped.contains("已停止"), "停止状态的统计信息应该显示'已停止'");
        
        // 启动清理任务
        RateLimitUtils.configure(1000L, 500L);
        
        // 运行状态的统计信息
        String statsWhenRunning = RateLimitUtils.getRateLimiterStats();
        log.info("运行状态统计: {}", statsWhenRunning);
        assertTrue(statsWhenRunning.contains("运行中"), "运行状态的统计信息应该显示'运行中'");
        
        log.info("✓ 统计信息验证通过");
    }
    
    @Test
    @DisplayName("验证手动清理功能不受定时任务影响")
    void testManualCleanupIndependentOfScheduler() throws InterruptedException {
        log.info("测试手动清理功能独立性");
        
        // 不启动定时清理任务，直接创建限流器
        String testKey = TEST_USER + "_test";
        RateLimitUtils.applyRateLimit(testKey, TEST_RATE_LIMIT, TEST_DATA_SIZE);
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "应该创建1个限流器");
        
        // 等待一段时间让限流器变为空闲状态
        Thread.sleep(100);
        
        // 手动执行清理（使用默认的空闲超时时间）
        RateLimitUtils.cleanupIdleRateLimiters();
        
        // 由于默认空闲超时时间较长（10分钟），限流器不应该被清理
        assertEquals(1, RateLimitUtils.getRateLimiterCount(), "使用默认超时时间，限流器不应该被清理");
        
        // 配置较短的空闲超时时间但不启动定时任务
        RateLimitUtils.configure(60000L, 50L); // 1分钟清理间隔，50ms空闲超时
        RateLimitUtils.stopCleanupScheduler(); // 立即停止定时任务
        
        // 等待超过空闲超时时间
        Thread.sleep(100);
        
        // 手动执行清理
        RateLimitUtils.cleanupIdleRateLimiters();
        
        // 现在限流器应该被清理
        assertEquals(0, RateLimitUtils.getRateLimiterCount(), "手动清理应该清除空闲的限流器");
        
        log.info("✓ 手动清理功能独立性验证通过");
    }
    
    @Test
    @DisplayName("验证配置更新会重启清理任务")
    void testConfigurationUpdateRestartsScheduler() {
        log.info("测试配置更新重启清理任务");
        
        // 第一次配置
        RateLimitUtils.configure(1000L, 500L);
        assertTrue(RateLimitUtils.isCleanupRunning(), "第一次配置后清理任务应该启动");
        
        String firstConfig = RateLimitUtils.getConfigurationInfo();
        assertTrue(firstConfig.contains("1000ms"), "第一次配置应该包含1000ms清理间隔");
        
        // 更新配置
        RateLimitUtils.configure(2000L, 1000L);
        assertTrue(RateLimitUtils.isCleanupRunning(), "更新配置后清理任务应该仍在运行");
        
        String secondConfig = RateLimitUtils.getConfigurationInfo();
        assertTrue(secondConfig.contains("2000ms"), "更新后配置应该包含2000ms清理间隔");
        assertTrue(secondConfig.contains("1000ms"), "更新后配置应该包含1000ms空闲超时");
        
        log.info("✓ 配置更新重启清理任务验证通过");
    }
}
