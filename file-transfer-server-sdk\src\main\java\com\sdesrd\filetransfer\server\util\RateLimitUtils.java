package com.sdesrd.filetransfer.server.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.google.common.util.concurrent.RateLimiter;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的限流工具类
 * 支持角色级别的精细化限速控制
 */
@Slf4j
public class RateLimitUtils {

    /** 限流器缓存 */
    private static final ConcurrentHashMap<String, RateLimiter> RATE_LIMITERS = new ConcurrentHashMap<>();

    /** 限流器最后使用时间缓存 */
    private static final ConcurrentHashMap<String, Long> LAST_USED_TIME = new ConcurrentHashMap<>();

    /**
     * 限流器清理间隔（毫秒） - 可配置
     * 功能：控制多久执行一次清理检查（定时任务的执行频率）
     * 作用：决定清理任务的调度间隔，每隔此时间执行一次 cleanupIdleRateLimiters()
     * 默认：300000ms（5分钟）
     * 配置建议：不要太短（避免频繁检查影响性能），通常为 idleTimeoutMs 的 1/2 到 1/3
     */
    private static long cleanupIntervalMs = 300000L; // 5分钟

    /**
     * 限流器空闲超时时间（毫秒） - 可配置
     * 功能：控制限流器空闲多久后被清理（判断是否清理的时间阈值）
     * 作用：决定限流器的生存时间，超过此时间未使用的限流器将被清理
     * 默认：600000ms（10分钟）
     * 配置建议：应大于 cleanupIntervalMs，给限流器足够的重用时间
     *
     * 两者协同工作示例：
     * - cleanupIntervalMs = 5分钟：每5分钟检查一次
     * - idleTimeoutMs = 10分钟：空闲超过10分钟的限流器被清理
     * 结果：限流器创建后，如果10分钟内未使用，将在下次5分钟检查时被清理
     */
    private static long idleTimeoutMs = 600000L; // 10分钟

    /** 定时清理任务调度器 */
    private static ScheduledExecutorService cleanupScheduler;

    /** 清理任务是否已启动 */
    private static final AtomicBoolean cleanupStarted = new AtomicBoolean(false);

    /**
     * 配置限流器参数
     *
     * @param cleanupInterval 清理间隔（毫秒）
     * @param idleTimeout 空闲超时时间（毫秒）
     */
    public static void configure(long cleanupInterval, long idleTimeout) {
        cleanupIntervalMs = cleanupInterval;
        idleTimeoutMs = idleTimeout;

        // 启动定时清理任务
        startCleanupScheduler();

        log.info("限流器配置更新 - 清理间隔: {}ms, 空闲超时: {}ms", cleanupInterval, idleTimeout);
    }

    /**
     * 启动定时清理任务调度器
     */
    private static synchronized void startCleanupScheduler() {
        if (cleanupStarted.get()) {
            // 如果已经启动，先停止现有的调度器
            stopCleanupScheduler();
        }

        cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "RateLimiter-Cleanup-Scheduler");
            thread.setDaemon(true);
            return thread;
        });

        // 启动定时清理任务，使用配置的清理间隔
        cleanupScheduler.scheduleAtFixedRate(
            RateLimitUtils::cleanupIdleRateLimiters,
            cleanupIntervalMs, // 初始延迟
            cleanupIntervalMs, // 执行间隔
            TimeUnit.MILLISECONDS
        );

        cleanupStarted.set(true);
        log.info("限流器定时清理任务已启动 - 清理间隔: {}ms", cleanupIntervalMs);
    }

    /**
     * 停止定时清理任务调度器
     */
    public static synchronized void stopCleanupScheduler() {
        if (cleanupScheduler != null && !cleanupScheduler.isShutdown()) {
            cleanupScheduler.shutdown();
            try {
                if (!cleanupScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            cleanupStarted.set(false);
            log.info("限流器定时清理任务已停止");
        }
    }
    
    /**
     * 应用限流
     * 
     * @param key 限流键（如用户名_upload或用户名_download）
     * @param permitsPerSecond 每秒允许的字节数
     * @param requestBytes 请求的字节数
     */
    public static void applyRateLimit(String key, long permitsPerSecond, long requestBytes) {
        if (permitsPerSecond <= 0) {
            return; // 不限速
        }
        
        RateLimiter rateLimiter = RATE_LIMITERS.computeIfAbsent(key, k -> {
            log.debug("创建限流器 - 键: {}, 速度: {} bytes/s", k, permitsPerSecond);
            return RateLimiter.create(permitsPerSecond);
        });
        
        // 更新限流器速度（如果配置有变化）
        rateLimiter.setRate(permitsPerSecond);

        // 更新最后使用时间
        LAST_USED_TIME.put(key, System.currentTimeMillis());

        // 申请令牌
        if (requestBytes > 0) {
            double waitTime = rateLimiter.acquire((int) requestBytes);
            if (waitTime > 0) {
                log.debug("限流等待 - 键: {}, 等待时间: {}s, 请求字节: {}", key, waitTime, requestBytes);
            }
        }
    }

    /**
     * 应用限流（带超时）
     *
     * @param key 限流键
     * @param permitsPerSecond 每秒允许的字节数
     * @param requestBytes 请求的字节数
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功获取令牌
     */
    public static boolean applyRateLimitWithTimeout(String key, long permitsPerSecond, long requestBytes, long timeoutMs) {
        if (permitsPerSecond <= 0) {
            return true; // 不限速
        }

        RateLimiter rateLimiter = RATE_LIMITERS.computeIfAbsent(key, k -> {
            log.debug("创建限流器 - 键: {}, 速度: {} bytes/s", k, permitsPerSecond);
            return RateLimiter.create(permitsPerSecond);
        });

        // 更新限流器速度
        rateLimiter.setRate(permitsPerSecond);

        // 更新最后使用时间
        LAST_USED_TIME.put(key, System.currentTimeMillis());

        // 尝试获取令牌
        if (requestBytes > 0) {
            boolean acquired = rateLimiter.tryAcquire((int) requestBytes, timeoutMs, TimeUnit.MILLISECONDS);
            if (!acquired) {
                log.warn("限流超时 - 键: {}, 超时时间: {}ms, 请求字节: {}", key, timeoutMs, requestBytes);
            }
            return acquired;
        }

        return true;
    }

    /**
     * 获取限流器当前速率
     *
     * @param key 限流键
     * @return 当前速率（permits/second），如果不存在返回-1
     */
    public static double getCurrentRate(String key) {
        RateLimiter rateLimiter = RATE_LIMITERS.get(key);
        return rateLimiter != null ? rateLimiter.getRate() : -1;
    }

    /**
     * 清理指定用户的限流器
     *
     * @param username 用户名
     */
    public static void clearUserRateLimiters(String username) {
        String uploadKey = username + "_upload";
        String downloadKey = username + "_download";

        RATE_LIMITERS.remove(uploadKey);
        RATE_LIMITERS.remove(downloadKey);
        LAST_USED_TIME.remove(uploadKey);
        LAST_USED_TIME.remove(downloadKey);

        log.debug("清理用户限流器 - 用户: {}", username);
    }

    /**
     * 清理限流器
     */
    public static void clearRateLimiter(String key) {
        RATE_LIMITERS.remove(key);
        LAST_USED_TIME.remove(key);
    }

    /**
     * 清理所有限流器
     */
    public static void clearAllRateLimiters() {
        log.info("清理所有限流器，当前数量: {}", RATE_LIMITERS.size());
        RATE_LIMITERS.clear();
        LAST_USED_TIME.clear();
    }

    /**
     * 清理空闲的限流器
     */
    public static void cleanupIdleRateLimiters() {
        long currentTime = System.currentTimeMillis();
        int initialCount = RATE_LIMITERS.size();

        LAST_USED_TIME.entrySet().removeIf(entry -> {
            String key = entry.getKey();
            long lastUsed = entry.getValue();

            if (currentTime - lastUsed > idleTimeoutMs) {
                RATE_LIMITERS.remove(key);
                log.debug("清理空闲限流器 - 键: {}, 空闲时间: {}ms", key, currentTime - lastUsed);
                return true;
            }
            return false;
        });

        int finalCount = RATE_LIMITERS.size();
        int cleanedCount = initialCount - finalCount;

        if (cleanedCount > 0) {
            log.info("清理空闲限流器完成，清理数量: {}, 剩余数量: {}", cleanedCount, finalCount);
        } else {
            log.debug("清理空闲限流器完成，无需清理，当前数量: {}", finalCount);
        }
    }

    /**
     * 获取当前限流器数量
     */
    public static int getRateLimiterCount() {
        return RATE_LIMITERS.size();
    }

    /**
     * 获取限流器统计信息
     */
    public static String getRateLimiterStats() {
        return String.format("限流器统计 - 总数: %d, 活跃数: %d, 清理任务状态: %s",
                RATE_LIMITERS.size(),
                LAST_USED_TIME.size(),
                cleanupStarted.get() ? "运行中" : "已停止");
    }

    /**
     * 获取当前配置信息
     */
    public static String getConfigurationInfo() {
        return String.format("限流器配置 - 清理间隔: %dms, 空闲超时: %dms, 清理任务: %s",
                cleanupIntervalMs,
                idleTimeoutMs,
                cleanupStarted.get() ? "运行中" : "已停止");
    }

    /**
     * 检查清理任务是否正在运行
     */
    public static boolean isCleanupRunning() {
        return cleanupStarted.get();
    }
}