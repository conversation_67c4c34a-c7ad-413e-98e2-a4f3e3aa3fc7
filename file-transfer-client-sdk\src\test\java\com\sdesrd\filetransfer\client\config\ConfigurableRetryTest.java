package com.sdesrd.filetransfer.client.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.client.util.RetryManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 可配置重传机制测试
 */
@Slf4j
@DisplayName("可配置重传机制测试")
class ConfigurableRetryTest {
    
    @Test
    @DisplayName("测试默认重传配置")
    void testDefaultRetryConfig() {
        log.info("[测试开始] 测试默认重传配置");
        
        ClientConfig config = ClientConfigBuilder.create()
                .auth("test", "test-secret")
                .retry(3, 1000)
                .build();
        
        // 验证默认配置
        assertEquals(3, config.getRetryCount());
        assertEquals(1000L, config.getRetryIntervalMs());
        
        // 验证生效配置
        assertEquals(3, config.getEffectiveFileRetryCount());
        assertEquals(1000L, config.getEffectiveFileRetryIntervalMs());
        assertEquals(1, config.getEffectiveChunkRetryCount()); // max(1, 3/2) = 1
        assertEquals(200L, config.getEffectiveChunkRetryIntervalMs()); // max(100, 1000/5) = 200
        
        log.info("[测试通过] 默认重传配置正确");
    }
    
    @Test
    @DisplayName("测试自定义重传配置")
    void testCustomRetryConfig() {
        log.info("[测试开始] 测试自定义重传配置");
        
        ClientConfig config = ClientConfigBuilder.create()
                .auth("test", "test-secret")
                .retry(6, 2000)
                .build();
        
        // 验证文件级配置
        assertEquals(6, config.getEffectiveFileRetryCount());
        assertEquals(2000L, config.getEffectiveFileRetryIntervalMs());
        
        // 验证分块级配置自动计算
        assertEquals(3, config.getEffectiveChunkRetryCount()); // max(1, 6/2) = 3
        assertEquals(400L, config.getEffectiveChunkRetryIntervalMs()); // max(100, 2000/5) = 400
        
        log.info("[测试通过] 自定义重传配置正确");
    }
    
    @Test
    @DisplayName("测试重传策略创建")
    void testRetryPolicyCreation() {
        log.info("[测试开始] 测试重传策略创建");
        
        ClientConfig config = ClientConfigBuilder.create()
                .auth("test", "test-secret")
                .retry(4, 1500)
                .build();
        
        // 测试文件级重传策略
        RetryManager.RetryPolicy filePolicy = RetryManager.createFileRetryPolicy(config);
        assertNotNull(filePolicy);
        assertEquals(4, filePolicy.getMaxRetries());
        assertEquals(1500L, filePolicy.getBaseDelayMs());
        assertEquals(15000L, filePolicy.getMaxDelayMs()); // 1500 * 10
        assertEquals(1.5, filePolicy.getBackoffMultiplier());
        assertEquals(0.2, filePolicy.getJitterFactor());
        
        // 测试分块级重传策略
        RetryManager.RetryPolicy chunkPolicy = RetryManager.createChunkRetryPolicy(config);
        assertNotNull(chunkPolicy);
        assertEquals(2, chunkPolicy.getMaxRetries()); // max(1, 4/2) = 2
        assertEquals(300L, chunkPolicy.getBaseDelayMs()); // max(100, 1500/5) = 300
        assertEquals(1500L, chunkPolicy.getMaxDelayMs()); // 300 * 5
        assertEquals(2.0, chunkPolicy.getBackoffMultiplier());
        assertEquals(0.1, chunkPolicy.getJitterFactor());
        
        log.info("[测试通过] 重传策略创建正确");
    }
    
    @Test
    @DisplayName("测试边界值配置")
    void testBoundaryValues() {
        log.info("[测试开始] 测试边界值配置");
        
        // 测试最小值
        ClientConfig minConfig = ClientConfigBuilder.create()
                .auth("test", "test-secret")
                .retry(0, 50)
                .build();
        
        assertEquals(1, minConfig.getEffectiveChunkRetryCount()); // max(1, 0/2) = 1
        assertEquals(100L, minConfig.getEffectiveChunkRetryIntervalMs()); // max(100, 50/5) = 100
        
        // 测试大值
        ClientConfig maxConfig = ClientConfigBuilder.create()
                .auth("test", "test-secret")
                .retry(20, 10000)
                .build();
        
        assertEquals(20, maxConfig.getEffectiveFileRetryCount());
        assertEquals(10000L, maxConfig.getEffectiveFileRetryIntervalMs());
        assertEquals(10, maxConfig.getEffectiveChunkRetryCount()); // 20/2 = 10
        assertEquals(2000L, maxConfig.getEffectiveChunkRetryIntervalMs()); // 10000/5 = 2000
        
        log.info("[测试通过] 边界值配置正确");
    }
    
    @Test
    @DisplayName("测试兼容性")
    void testBackwardCompatibility() {
        log.info("[测试开始] 测试向后兼容性");
        
        // 测试传统配置方式
        ClientConfig oldStyleConfig = ClientConfigBuilder.create()
                .auth("test", "test-secret")
                .retryCount(4)
                .retryInterval(1200)
                .build();
        
        assertEquals(4, oldStyleConfig.getEffectiveFileRetryCount());
        assertEquals(1200L, oldStyleConfig.getEffectiveFileRetryIntervalMs());
        assertEquals(2, oldStyleConfig.getEffectiveChunkRetryCount()); // max(1, 4/2) = 2
        assertEquals(240L, oldStyleConfig.getEffectiveChunkRetryIntervalMs()); // max(100, 1200/5) = 240
        
        // 验证策略创建兼容性
        RetryManager.RetryPolicy filePolicy = RetryManager.createFileRetryPolicy(oldStyleConfig);
        RetryManager.RetryPolicy chunkPolicy = RetryManager.createChunkRetryPolicy(oldStyleConfig);
        
        assertNotNull(filePolicy);
        assertNotNull(chunkPolicy);
        
        log.info("[测试通过] 向后兼容性正确");
    }
} 