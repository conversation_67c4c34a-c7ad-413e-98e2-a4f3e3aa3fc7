package com.sdesrd.filetransfer.server.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.server.util.PerformanceMonitor;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 配置参数生效性测试
 * 验证所有新增的配置项能够正确生效，硬编码值已被配置值替代
 */
@Slf4j
@DisplayName("配置参数生效性测试")
class ConfigurationEffectivenessTest {
    
    private FileTransferProperties properties;
    
    @BeforeEach
    void setUp() {
        properties = new FileTransferProperties();
        // 设置测试配置值
        properties.setApiSignatureTtlSeconds(600L);
        properties.setDatabaseBackupIntervalHours(48);
        properties.setDatabaseBackupRetentionDays(14);
        properties.setDatabaseHealthCheckTimeoutMs(10000L);
        properties.setDatabaseHealthCheckIntervalMs(60000L);
        properties.setPerformanceMonitorIntervalSeconds(60);
        properties.setPerformanceMetricsRetentionMs(7200000L);
        properties.setRateLimiterCleanupIntervalMs(600000L);
        properties.setRateLimiterIdleTimeoutMs(1200000L);
        
        log.info("配置参数生效性测试 - 开始验证所有新增配置项");
    }
    
    @Test
    @DisplayName("验证API签名时效配置生效")
    void testApiSignatureTtlConfiguration() {
        log.info("测试API签名时效配置 - 期望值: 600秒");
        
        assertEquals(600L, properties.getApiSignatureTtlSeconds(), 
            "API签名时效配置应该生效");
        
        log.info("✓ API签名时效配置验证通过: {}秒", properties.getApiSignatureTtlSeconds());
    }
    
    @Test
    @DisplayName("验证数据库备份配置生效")
    void testDatabaseBackupConfiguration() {
        log.info("测试数据库备份配置 - 期望备份间隔: 48小时, 保留期: 14天");
        
        assertEquals(48, properties.getDatabaseBackupIntervalHours(), 
            "数据库备份间隔配置应该生效");
        assertEquals(14, properties.getDatabaseBackupRetentionDays(), 
            "数据库备份保留期配置应该生效");
        
        log.info("✓ 数据库备份配置验证通过 - 间隔: {}小时, 保留期: {}天", 
            properties.getDatabaseBackupIntervalHours(), 
            properties.getDatabaseBackupRetentionDays());
    }
    
    @Test
    @DisplayName("验证数据库健康检查配置生效")
    void testDatabaseHealthCheckConfiguration() {
        log.info("测试数据库健康检查配置 - 期望超时: 10000ms, 间隔: 60000ms");
        
        assertEquals(10000L, properties.getDatabaseHealthCheckTimeoutMs(), 
            "数据库健康检查超时配置应该生效");
        assertEquals(60000L, properties.getDatabaseHealthCheckIntervalMs(), 
            "数据库健康检查间隔配置应该生效");
        
        log.info("✓ 数据库健康检查配置验证通过 - 超时: {}ms, 间隔: {}ms", 
            properties.getDatabaseHealthCheckTimeoutMs(), 
            properties.getDatabaseHealthCheckIntervalMs());
    }
    
    @Test
    @DisplayName("验证性能监控配置生效")
    void testPerformanceMonitorConfiguration() {
        log.info("测试性能监控配置 - 期望间隔: 60秒, 保留时间: 7200000ms");
        
        assertEquals(60, properties.getPerformanceMonitorIntervalSeconds(), 
            "性能监控间隔配置应该生效");
        assertEquals(7200000L, properties.getPerformanceMetricsRetentionMs(), 
            "性能指标保留时间配置应该生效");
        
        // 测试PerformanceMonitor配置方法
        PerformanceMonitor.configure(
            properties.getPerformanceMonitorIntervalSeconds(),
            properties.getPerformanceMetricsRetentionMs()
        );
        
        log.info("✓ 性能监控配置验证通过 - 间隔: {}秒, 保留时间: {}ms", 
            properties.getPerformanceMonitorIntervalSeconds(), 
            properties.getPerformanceMetricsRetentionMs());
    }
    
    @Test
    @DisplayName("验证限流器配置生效")
    void testRateLimiterConfiguration() {
        log.info("测试限流器配置 - 期望清理间隔: 600000ms, 空闲超时: 1200000ms");
        
        assertEquals(600000L, properties.getRateLimiterCleanupIntervalMs(), 
            "限流器清理间隔配置应该生效");
        assertEquals(1200000L, properties.getRateLimiterIdleTimeoutMs(), 
            "限流器空闲超时配置应该生效");
        
        // 测试RateLimitUtils配置方法
        RateLimitUtils.configure(
            properties.getRateLimiterCleanupIntervalMs(),
            properties.getRateLimiterIdleTimeoutMs()
        );
        
        log.info("✓ 限流器配置验证通过 - 清理间隔: {}ms, 空闲超时: {}ms", 
            properties.getRateLimiterCleanupIntervalMs(), 
            properties.getRateLimiterIdleTimeoutMs());
    }
    
    @Test
    @DisplayName("验证配置项默认值正确")
    void testDefaultConfigurationValues() {
        log.info("测试配置项默认值");
        
        FileTransferProperties defaultProps = new FileTransferProperties();
        
        // 验证默认值
        assertEquals(300L, defaultProps.getApiSignatureTtlSeconds(), 
            "API签名时效默认值应该为300秒");
        assertEquals(24, defaultProps.getDatabaseBackupIntervalHours(), 
            "数据库备份间隔默认值应该为24小时");
        assertEquals(7, defaultProps.getDatabaseBackupRetentionDays(), 
            "数据库备份保留期默认值应该为7天");
        assertEquals(5000L, defaultProps.getDatabaseHealthCheckTimeoutMs(), 
            "数据库健康检查超时默认值应该为5000ms");
        assertEquals(30000L, defaultProps.getDatabaseHealthCheckIntervalMs(), 
            "数据库健康检查间隔默认值应该为30000ms");
        assertEquals(30, defaultProps.getPerformanceMonitorIntervalSeconds(), 
            "性能监控间隔默认值应该为30秒");
        assertEquals(3600000L, defaultProps.getPerformanceMetricsRetentionMs(), 
            "性能指标保留时间默认值应该为3600000ms");
        assertEquals(300000L, defaultProps.getRateLimiterCleanupIntervalMs(), 
            "限流器清理间隔默认值应该为300000ms");
        assertEquals(600000L, defaultProps.getRateLimiterIdleTimeoutMs(), 
            "限流器空闲超时默认值应该为600000ms");
        
        log.info("✓ 所有配置项默认值验证通过");
    }
    
    @Test
    @DisplayName("验证配置项向后兼容性")
    void testBackwardCompatibility() {
        log.info("测试配置项向后兼容性 - 确保新增配置不影响现有功能");
        
        FileTransferProperties props = new FileTransferProperties();
        
        // 验证原有配置项仍然正常工作
        assertTrue(props.isEnabled(), "服务启用状态应该保持默认值");
        assertEquals("./data/file-transfer/database.db", props.getDatabasePath(), 
            "数据库路径应该保持默认值");
        assertEquals(3600000L, props.getTokenExpire(), 
            "传输会话过期时间应该保持默认值");
        assertTrue(props.isCleanupEnabled(), "清理功能应该保持默认启用状态");
        
        log.info("✓ 向后兼容性验证通过 - 现有配置项未受影响");
    }
}
