package com.sdesrd.filetransfer.client.config;

/**
 * 客户端配置构建器（增强版）
 * 提供简洁统一的链式调用配置方式，所有配置项均在同一层级
 */
public class ClientConfigBuilder {
    
    private final ClientConfig config;
    
    /**
     * 私有构造函数
     */
    private ClientConfigBuilder() {
        this.config = new ClientConfig();
    }
    
    /**
     * 创建配置构建器
     * 
     * @return 配置构建器实例
     */
    public static ClientConfigBuilder create() {
        return new ClientConfigBuilder();
    }
    
    // ==================== 服务器连接配置方法 ====================
    
    /**
     * 设置服务器地址
     * 
     * @param serverAddr 服务器地址
     * @return 构建器实例
     */
    public ClientConfigBuilder serverAddr(String serverAddr) {
        config.setServerAddr(serverAddr);
        return this;
    }
    
    /**
     * 设置服务器端口
     * 
     * @param serverPort 服务器端口
     * @return 构建器实例
     */
    public ClientConfigBuilder serverPort(int serverPort) {
        config.setServerPort(serverPort);
        return this;
    }
    
    /**
     * 设置上下文路径
     * 
     * @param contextPath 上下文路径
     * @return 构建器实例
     */
    public ClientConfigBuilder contextPath(String contextPath) {
        config.setContextPath(contextPath);
        return this;
    }
    
    /**
     * 启用HTTPS协议
     * 
     * @return 构建器实例
     */
    public ClientConfigBuilder useHttps() {
        config.setUseHttps(true);
        return this;
    }
    
    /**
     * 设置是否使用HTTPS协议
     * 
     * @param useHttps 是否使用HTTPS
     * @return 构建器实例
     */
    public ClientConfigBuilder useHttps(boolean useHttps) {
        config.setUseHttps(useHttps);
        return this;
    }
    
    // ==================== 用户认证配置方法 ====================
    
    /**
     * 设置用户认证信息
     * 
     * @param user 用户名
     * @param secretKey 密钥
     * @return 构建器实例
     */
    public ClientConfigBuilder auth(String user, String secretKey) {
        config.setUser(user);
        config.setSecretKey(secretKey);
        return this;
    }
    
    /**
     * 设置用户名
     * 
     * @param user 用户名
     * @return 构建器实例
     */
    public ClientConfigBuilder user(String user) {
        config.setUser(user);
        return this;
    }
    
    /**
     * 设置用户密钥
     * 
     * @param secretKey 用户密钥
     * @return 构建器实例
     */
    public ClientConfigBuilder secretKey(String secretKey) {
        config.setSecretKey(secretKey);
        return this;
    }
    
    // ==================== 传输性能配置方法 ====================
    
    /**
     * 设置分片大小
     * 
     * @param chunkSize 分片大小（字节）
     * @return 构建器实例
     */
    public ClientConfigBuilder chunkSize(long chunkSize) {
        config.setChunkSize(chunkSize);
        return this;
    }
    
    /**
     * 设置连接超时时间
     * 
     * @param connectTimeoutSeconds 连接超时时间（秒）
     * @return 构建器实例
     */
    public ClientConfigBuilder connectTimeout(long connectTimeoutSeconds) {
        config.setConnectTimeoutSeconds(connectTimeoutSeconds);
        return this;
    }
    
    /**
     * 设置读取超时时间
     * 
     * @param readTimeoutSeconds 读取超时时间（秒）
     * @return 构建器实例
     */
    public ClientConfigBuilder readTimeout(long readTimeoutSeconds) {
        config.setReadTimeoutSeconds(readTimeoutSeconds);
        return this;
    }
    
    /**
     * 设置写入超时时间
     * 
     * @param writeTimeoutSeconds 写入超时时间（秒）
     * @return 构建器实例
     */
    public ClientConfigBuilder writeTimeout(long writeTimeoutSeconds) {
        config.setWriteTimeoutSeconds(writeTimeoutSeconds);
        return this;
    }
    
    /**
     * 设置所有超时时间
     * 
     * @param timeoutSeconds 超时时间（秒），应用于连接、读取和写入超时
     * @return 构建器实例
     */
    public ClientConfigBuilder timeouts(long timeoutSeconds) {
        config.setConnectTimeoutSeconds(timeoutSeconds);
        config.setReadTimeoutSeconds(timeoutSeconds);
        config.setWriteTimeoutSeconds(timeoutSeconds);
        return this;
    }
    
    // ==================== 并发控制配置方法 ====================
    
    /**
     * 设置最大并发传输数
     *
     * @param maxConcurrentTransfers 最大并发传输数
     * @return 构建器实例
     */
    public ClientConfigBuilder maxConcurrentTransfers(int maxConcurrentTransfers) {
        config.setMaxConcurrentTransfers(maxConcurrentTransfers);
        return this;
    }
    
    /**
     * 设置最大空闲连接数
     * 
     * @param maxIdleConnections 最大空闲连接数
     * @return 构建器实例
     */
    public ClientConfigBuilder maxIdleConnections(int maxIdleConnections) {
        config.setMaxIdleConnections(maxIdleConnections);
        return this;
    }
    
    /**
     * 设置连接保活时间
     * 
     * @param keepAliveDurationMinutes 连接保活时间（分钟）
     * @return 构建器实例
     */
    public ClientConfigBuilder keepAliveDuration(long keepAliveDurationMinutes) {
        config.setKeepAliveDurationMinutes(keepAliveDurationMinutes);
        return this;
    }
    
    // ==================== 重试策略配置方法 ====================
    
    /**
     * 设置重试配置
     *
     * @param retryCount 重试次数
     * @param retryIntervalMs 重试间隔（毫秒）
     * @return 构建器实例
     */
    public ClientConfigBuilder retry(int retryCount, long retryIntervalMs) {
        config.setRetryCount(retryCount);
        config.setRetryIntervalMs(retryIntervalMs);
        return this;
    }
    
    /**
     * 设置重试次数
     * 
     * @param retryCount 重试次数
     * @return 构建器实例
     */
    public ClientConfigBuilder retryCount(int retryCount) {
        config.setRetryCount(retryCount);
        return this;
    }
    
    /**
     * 设置重试间隔
     * 
     * @param retryIntervalMs 重试间隔（毫秒）
     * @return 构建器实例
     */
    public ClientConfigBuilder retryInterval(long retryIntervalMs) {
        config.setRetryIntervalMs(retryIntervalMs);
        return this;
    }
    

    
    // ==================== 构建方法 ====================
    
    /**
     * 构建配置对象
     * 
     * @return 配置对象
     * @throws IllegalStateException 如果配置不完整或无效
     */
    public ClientConfig build() {
        config.validateConfig();
        return config;
    }
    
    // ==================== 便捷构建方法 ====================
    
    /**
     * 创建默认配置
     * 
     * @param user 用户名
     * @param secretKey 密钥
     * @return 默认配置
     */
    public static ClientConfig defaultConfig(String user, String secretKey) {
        return create()
                .auth(user, secretKey)
                .build();
    }
    
    /**
     * 创建本地开发配置
     *
     * @param user 用户名
     * @param secretKey 密钥
     * @return 本地开发配置
     */
    public static ClientConfig localConfig(String user, String secretKey) {
        return create()
                .serverAddr("localhost")
                .serverPort(49011)
                .auth(user, secretKey)
                .chunkSize(1024 * 1024) // 1MB
                .maxConcurrentTransfers(3)
                .retry(3, 1000)
                .build();
    }
    
    /**
     * 创建生产环境配置
     *
     * @param serverAddr 服务器地址
     * @param serverPort 服务器端口
     * @param user 用户名
     * @param secretKey 密钥
     * @return 生产环境配置
     */
    public static ClientConfig productionConfig(String serverAddr, int serverPort, String user, String secretKey) {
        return create()
                .serverAddr(serverAddr)
                .serverPort(serverPort)
                .useHttps()
                .auth(user, secretKey)
                .chunkSize(2 * 1024 * 1024) // 2MB
                .maxConcurrentTransfers(5)
                .retry(5, 2000)
                .build();
    }
    
    /**
     * 创建快速连接配置（最小化配置）
     * 
     * @param serverAddr 服务器地址
     * @param user 用户名
     * @param secretKey 密钥
     * @return 快速连接配置
     */
    public static ClientConfig quickConnect(String serverAddr, String user, String secretKey) {
        return create()
                .serverAddr(serverAddr)
                .auth(user, secretKey)
                .build();
    }
    
    /**
     * 创建高性能配置（优化传输性能）
     * 
     * @param serverAddr 服务器地址
     * @param user 用户名
     * @param secretKey 密钥
     * @return 高性能配置
     */
    public static ClientConfig highPerformanceConfig(String serverAddr, String user, String secretKey) {
        return create()
                .serverAddr(serverAddr)
                .auth(user, secretKey)
                .chunkSize(4 * 1024 * 1024) // 4MB分块
                .maxConcurrentTransfers(8) // 更高并发
                .timeouts(120) // 更长超时时间
                .retry(3, 500) // 更快重试
                .build();
    }


}
