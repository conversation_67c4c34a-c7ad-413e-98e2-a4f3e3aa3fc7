package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库管理服务
 * 提供数据库备份、恢复、重建和健康检查功能
 */
@Slf4j
@Service
public class DatabaseManagementService {
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;

    @Autowired
    private DatabaseFallbackService databaseFallbackService;

    /**
     * 定时任务执行器
     */
    private ScheduledExecutorService scheduledExecutor;
    
    /**
     * 备份文件名格式化器
     */
    private static final DateTimeFormatter BACKUP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /**
     * 备份文件扩展名
     */
    private static final String BACKUP_EXTENSION = ".backup.db";
    
    /**
     * 获取自动备份间隔（小时）
     */
    private int getAutoBackupIntervalHours() {
        return properties.getDatabaseBackupIntervalHours();
    }

    /**
     * 获取备份文件保留天数
     */
    private int getBackupRetentionDays() {
        return properties.getDatabaseBackupRetentionDays();
    }

    /**
     * 数据库重建状态锁
     * 使用 AtomicBoolean 实现简单的分布式锁，防止并发重建
     */
    private final AtomicBoolean rebuildInProgress = new AtomicBoolean(false);

    /**
     * 当前重建操作信息
     */
    private final AtomicReference<RebuildOperationInfo> currentRebuildInfo = new AtomicReference<>();
    
    @PostConstruct
    public void init() {
        // 启动定时备份任务
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "database-backup-scheduler");
            thread.setDaemon(true);
            return thread;
        });
        
        // 每24小时执行一次自动备份
        int backupInterval = getAutoBackupIntervalHours();
        scheduledExecutor.scheduleAtFixedRate(
            this::performAutoBackup,
            backupInterval,
            backupInterval,
            TimeUnit.HOURS
        );

        log.info("数据库管理服务初始化完成，自动备份间隔: {}小时", backupInterval);
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("数据库管理服务已关闭");
    }
    
    /**
     * 检查数据库健康状态
     * 
     * @return 健康状态信息
     */
    public DatabaseHealthInfo checkDatabaseHealth() {
        DatabaseHealthInfo healthInfo = new DatabaseHealthInfo();
        
        try {
            // 检查数据库连接
            try (Connection connection = dataSource.getConnection()) {
                healthInfo.setConnectionAvailable(true);
                healthInfo.setDatabasePath(properties.getDatabasePath());

                // 检查数据库文件
                File dbFile = new File(properties.getDatabasePath());
                healthInfo.setDatabaseFileExists(dbFile.exists());
                healthInfo.setDatabaseFileSize(dbFile.exists() ? dbFile.length() : 0);
                
                // 检查表是否可访问
                try {
                    QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
                    query.last("LIMIT 1");
                    transferRecordMapper.selectList(query);
                    healthInfo.setTablesAccessible(true);
                } catch (Exception e) {
                    log.warn("数据库表访问检查失败", e);
                    healthInfo.setTablesAccessible(false);
                    healthInfo.setErrorMessage("数据库表不可访问: " + e.getMessage());
                }
                
                // 统计记录数量
                try {
                    long totalRecords = transferRecordMapper.selectCount(null);
                    healthInfo.setTotalRecords(totalRecords);
                } catch (Exception e) {
                    log.warn("统计记录数量失败", e);
                    healthInfo.setTotalRecords(-1L);
                }
                
            }
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            healthInfo.setConnectionAvailable(false);
            healthInfo.setErrorMessage("数据库连接失败: " + e.getMessage());
        }
        
        healthInfo.setCheckTime(LocalDateTime.now().toString());
        return healthInfo;
    }
    
    /**
     * 创建数据库备份
     * 
     * @return 备份文件路径
     */
    public String createBackup() {
        try {
            String dbPath = properties.getDatabasePath();
            File dbFile = new File(dbPath);
            
            if (!dbFile.exists()) {
                throw new FileTransferException("数据库文件不存在: " + dbPath);
            }
            
            // 生成备份文件名
            String timestamp = LocalDateTime.now().format(BACKUP_FORMATTER);
            String backupFileName = "database_" + timestamp + BACKUP_EXTENSION;
            
            // 确保备份目录存在
            File dbDir = dbFile.getParentFile();
            File backupDir = new File(dbDir, "backups");
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }
            
            File backupFile = new File(backupDir, backupFileName);
            
            // 执行WAL checkpoint确保所有数据写入主文件
            // 这样可以避免备份不一致的问题
            try {
                log.info("执行WAL checkpoint，确保数据一致性...");
                jdbcTemplate.execute("PRAGMA wal_checkpoint(FULL)");
                log.info("WAL checkpoint执行成功");
            } catch (Exception e) {
                log.warn("WAL checkpoint执行失败，将继续备份并包含WAL文件: {}", e.getMessage());
            }
            
            // 复制主数据库文件
            Files.copy(dbFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            log.info("主数据库文件备份完成: {}", backupFile.getAbsolutePath());
            
            // 检查并备份WAL和SHM文件（如果存在）
            backupWalAndShmFiles(dbPath, backupFile.getAbsolutePath());
            
            log.info("数据库备份创建成功: {}", backupFile.getAbsolutePath());
            return backupFile.getAbsolutePath();
            
        } catch (Exception e) {
            log.error("创建数据库备份失败", e);
            throw new FileTransferException("创建数据库备份失败: " + e.getMessage());
        }
    }
    
    /**
     * 备份WAL和SHM文件
     * 
     * @param originalDbPath 原始数据库文件路径
     * @param backupDbPath 备份数据库文件路径
     */
    private void backupWalAndShmFiles(String originalDbPath, String backupDbPath) {
        try {
            // 备份WAL文件
            File walFile = new File(originalDbPath + "-wal");
            if (walFile.exists() && walFile.length() > 0) {
                File backupWalFile = new File(backupDbPath + "-wal");
                Files.copy(walFile.toPath(), backupWalFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                log.info("WAL文件备份完成: {} ({} bytes)", backupWalFile.getAbsolutePath(), walFile.length());
            } else {
                log.debug("WAL文件不存在或为空，跳过备份");
            }
            
            // 备份SHM文件
            File shmFile = new File(originalDbPath + "-shm");
            if (shmFile.exists() && shmFile.length() > 0) {
                File backupShmFile = new File(backupDbPath + "-shm");
                Files.copy(shmFile.toPath(), backupShmFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                log.info("SHM文件备份完成: {} ({} bytes)", backupShmFile.getAbsolutePath(), shmFile.length());
            } else {
                log.debug("SHM文件不存在或为空，跳过备份");
            }
            
        } catch (Exception e) {
            log.warn("备份WAL/SHM文件时出现警告（不影响主备份）: {}", e.getMessage());
        }
    }
    
    /**
     * 获取所有备份文件列表
     * 
     * @return 备份文件信息列表
     */
    public List<BackupFileInfo> listBackups() {
        List<BackupFileInfo> backups = new ArrayList<>();
        
        try {
            String dbPath = properties.getDatabasePath();
            File dbFile = new File(dbPath);
            File backupDir = new File(dbFile.getParentFile(), "backups");
            
            if (!backupDir.exists() || !backupDir.isDirectory()) {
                return backups;
            }
            
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(
                    backupDir.toPath(), "*" + BACKUP_EXTENSION)) {
                
                for (Path backupPath : stream) {
                    File backupFile = backupPath.toFile();
                    BackupFileInfo info = new BackupFileInfo();
                    info.setFileName(backupFile.getName());
                    info.setFilePath(backupFile.getAbsolutePath());
                    
                    // 计算备份总大小（包括WAL和SHM文件）
                    long totalSize = backupFile.length();
                    File walFile = new File(backupFile.getAbsolutePath() + "-wal");
                    File shmFile = new File(backupFile.getAbsolutePath() + "-shm");
                    
                    if (walFile.exists()) {
                        totalSize += walFile.length();
                    }
                    if (shmFile.exists()) {
                        totalSize += shmFile.length();
                    }
                    
                    info.setFileSize(totalSize);
                    info.setCreateTime(backupFile.lastModified());
                    info.setFormattedSize(formatFileSize(totalSize));
                    info.setFormattedTime(formatTime(backupFile.lastModified()));
                    
                    // 添加WAL/SHM文件信息到备份信息中
                    StringBuilder fileDetails = new StringBuilder();
                    fileDetails.append("主文件: ").append(formatFileSize(backupFile.length()));
                    if (walFile.exists()) {
                        fileDetails.append(", WAL: ").append(formatFileSize(walFile.length()));
                    }
                    if (shmFile.exists()) {
                        fileDetails.append(", SHM: ").append(formatFileSize(shmFile.length()));
                    }
                    info.setFileDetails(fileDetails.toString());
                    
                    backups.add(info);
                }
            }
            
            // 按创建时间倒序排列
            backups.sort((a, b) -> Long.compare(b.getCreateTime(), a.getCreateTime()));
            
        } catch (Exception e) {
            log.error("获取备份文件列表失败", e);
        }
        
        return backups;
    }
    
    /**
     * 下载备份文件
     * 
     * @param fileName 备份文件名
     * @return 备份文件对象
     */
    public File getBackupFile(String fileName) {
        try {
            String dbPath = properties.getDatabasePath();
            File dbFile = new File(dbPath);
            File backupDir = new File(dbFile.getParentFile(), "backups");
            File backupFile = new File(backupDir, fileName);
            
            // 安全检查：确保文件在备份目录内
            if (!backupFile.getCanonicalPath().startsWith(backupDir.getCanonicalPath())) {
                throw new FileTransferException("非法的备份文件路径: " + fileName);
            }
            
            if (!backupFile.exists() || !backupFile.isFile()) {
                throw new FileTransferException("备份文件不存在: " + fileName);
            }
            
            if (!fileName.endsWith(BACKUP_EXTENSION)) {
                throw new FileTransferException("非法的备份文件格式: " + fileName);
            }
            
            return backupFile;
            
        } catch (IOException e) {
            log.error("获取备份文件失败: {}", fileName, e);
            throw new FileTransferException("获取备份文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 通过磁盘扫描重建数据库
     * 支持多用户存储路径扫描，包含并发控制
     *
     * @return 重建结果信息
     */
    @Transactional
    public DatabaseRebuildResult rebuildDatabaseFromDisk() {
        return rebuildDatabaseFromDisk(null, null);
    }

    /**
     * 通过磁盘扫描重建数据库（带用户信息）
     * 支持多用户存储路径扫描，包含并发控制
     *
     * @param initiatorUser 发起用户
     * @param initiatorIp 发起IP
     * @return 重建结果信息
     */
    @Transactional
    public DatabaseRebuildResult rebuildDatabaseFromDisk(String initiatorUser, String initiatorIp) {
        DatabaseRebuildResult result = new DatabaseRebuildResult();
        result.setStartTime(LocalDateTime.now().toString());

        // 1. 检查是否已有重建操作在进行
        if (!rebuildInProgress.compareAndSet(false, true)) {
            RebuildOperationInfo currentOp = currentRebuildInfo.get();
            String errorMsg = String.format("数据库重建操作已在进行中，请稍后再试。当前操作: %s",
                                           currentOp != null ? currentOp.toString() : "未知操作");
            log.warn("拒绝并发重建请求 - 发起用户: {}, IP: {}, 原因: {}", initiatorUser, initiatorIp, errorMsg);

            result.setSuccess(false);
            result.setErrorMessage(errorMsg);
            result.setEndTime(LocalDateTime.now().toString());
            return result;
        }

        // 2. 记录当前重建操作信息
        String operationId = java.util.UUID.randomUUID().toString().substring(0, 8);
        RebuildOperationInfo operationInfo = new RebuildOperationInfo(
            operationId, result.getStartTime(), initiatorUser, initiatorIp);
        currentRebuildInfo.set(operationInfo);

        try {
            log.info("开始通过多用户存储路径扫描重建数据库 - 操作ID: {}, 发起用户: {}, IP: {}",
                    operationId, initiatorUser, initiatorIp);

            // 清空现有记录
            transferRecordMapper.delete(null);
            log.info("已清空现有数据库记录");

            // 使用DatabaseFallbackService的多用户扫描功能
            Map<String, Object> scanResult = databaseFallbackService.scanAndRebuildFromMetadata();

            // 提取扫描结果
            boolean scanSuccess = (Boolean) scanResult.getOrDefault("success", false);
            int scannedFiles = (Integer) scanResult.getOrDefault("scannedFiles", 0);
            int rebuiltRecords = (Integer) scanResult.getOrDefault("rebuiltRecords", 0);
            int skippedFiles = (Integer) scanResult.getOrDefault("skippedFiles", 0);
            @SuppressWarnings("unchecked")
            List<String> errors = (List<String>) scanResult.getOrDefault("errors", new ArrayList<>());
            @SuppressWarnings("unchecked")
            Map<String, Object> userResults = (Map<String, Object>) scanResult.getOrDefault("userResults", new HashMap<>());

            // 设置结果
            result.setScannedFiles(scannedFiles);
            result.setRebuiltRecords(rebuiltRecords);
            result.setFailedFiles(skippedFiles);
            result.setEndTime(LocalDateTime.now().toString());
            result.setSuccess(scanSuccess);

            // 构建详细消息
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append(String.format("多用户扫描重建完成 - 扫描: %d, 重建: %d, 跳过: %d",
                    scannedFiles, rebuiltRecords, skippedFiles));

            if (!userResults.isEmpty()) {
                messageBuilder.append("\n\n用户扫描详情:");
                for (Map.Entry<String, Object> entry : userResults.entrySet()) {
                    String userKey = entry.getKey();
                    @SuppressWarnings("unchecked")
                    Map<String, Object> userResult = (Map<String, Object>) entry.getValue();

                    int userScanned = (Integer) userResult.getOrDefault("scannedFiles", 0);
                    int userRebuilt = (Integer) userResult.getOrDefault("rebuiltRecords", 0);
                    int userSkipped = (Integer) userResult.getOrDefault("skippedFiles", 0);
                    String pathDesc = (String) userResult.getOrDefault("pathDescription", userKey);
                    String storagePath = (String) userResult.getOrDefault("storagePath", "未知");

                    messageBuilder.append(String.format("\n  %s:", pathDesc));
                    messageBuilder.append(String.format("\n    路径: %s", storagePath));
                    messageBuilder.append(String.format("\n    结果: 扫描=%d, 重建=%d, 跳过=%d",
                            userScanned, userRebuilt, userSkipped));
                }
            }

            if (!errors.isEmpty()) {
                messageBuilder.append(String.format("\n\n警告信息 (%d 个):", errors.size()));
                for (int i = 0; i < Math.min(errors.size(), 10); i++) { // 最多显示10个错误
                    messageBuilder.append("\n  - ").append(errors.get(i));
                }
                if (errors.size() > 10) {
                    messageBuilder.append(String.format("\n  ... 还有 %d 个错误", errors.size() - 10));
                }
            }

            result.setMessage(messageBuilder.toString());

            log.info("多用户数据库重建完成 - 操作ID: {}, 扫描: {}, 重建: {}, 跳过: {}, 错误: {}",
                operationId, scannedFiles, rebuiltRecords, skippedFiles, errors.size());

        } catch (Exception e) {
            log.error("数据库重建失败 - 操作ID: {}", operationId, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now().toString());
        } finally {
            // 3. 释放重建锁和清理操作信息
            currentRebuildInfo.set(null);
            rebuildInProgress.set(false);
            log.info("数据库重建操作完成，释放并发锁 - 操作ID: {}", operationId);
        }

        return result;
    }

    /**
     * 检查数据库重建状态
     *
     * @return 重建状态信息
     */
    public Map<String, Object> getRebuildStatus() {
        Map<String, Object> status = new HashMap<>();

        boolean inProgress = rebuildInProgress.get();
        status.put("inProgress", inProgress);

        if (inProgress) {
            RebuildOperationInfo currentOp = currentRebuildInfo.get();
            if (currentOp != null) {
                status.put("operationId", currentOp.getOperationId());
                status.put("startTime", currentOp.getStartTime());
                status.put("initiatorUser", currentOp.getInitiatorUser());
                status.put("initiatorIp", currentOp.getInitiatorIp());

                // 计算运行时间
                try {
                    LocalDateTime startTime = LocalDateTime.parse(currentOp.getStartTime());
                    LocalDateTime now = LocalDateTime.now();
                    long durationMinutes = java.time.Duration.between(startTime, now).toMinutes();
                    status.put("durationMinutes", durationMinutes);
                } catch (Exception e) {
                    status.put("durationMinutes", "未知");
                }
            }
        }

        return status;
    }

    /**
     * 执行自动备份
     */
    private void performAutoBackup() {
        try {
            log.info("开始执行自动数据库备份");
            String backupPath = createBackup();
            
            // 清理过期备份
            cleanupOldBackups();
            
            log.info("自动数据库备份完成: {}", backupPath);
            
        } catch (Exception e) {
            log.error("自动数据库备份失败", e);
        }
    }
    
    /**
     * 清理过期备份文件
     */
    private void cleanupOldBackups() {
        try {
            List<BackupFileInfo> backups = listBackups();
            long cutoffTime = System.currentTimeMillis() - (getBackupRetentionDays() * 24 * 60 * 60 * 1000L);
            
            int deletedCount = 0;
            for (BackupFileInfo backup : backups) {
                if (backup.getCreateTime() < cutoffTime) {
                    File backupFile = new File(backup.getFilePath());
                    if (backupFile.delete()) {
                        deletedCount++;
                        log.debug("删除过期备份文件: {}", backup.getFileName());
                    }
                }
            }
            
            if (deletedCount > 0) {
                log.info("清理过期备份文件完成，删除数量: {}", deletedCount);
            }
            
        } catch (Exception e) {
            log.error("清理过期备份文件失败", e);
        }
    }
    
    /**
     * 扫描目录并重建记录
     */
    private DatabaseRebuildResult scanAndRebuildRecords(File dir, DatabaseRebuildResult result) {
        File[] files = dir.listFiles();
        if (files == null) {
            return result;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                scanAndRebuildRecords(file, result);
            } else if (file.isFile()) {
                result.setScannedFiles(result.getScannedFiles() + 1);
                
                try {
                    // 尝试重建文件记录
                    if (rebuildFileRecord(file)) {
                        result.setRebuiltRecords(result.getRebuiltRecords() + 1);
                    }
                } catch (Exception e) {
                    log.warn("重建文件记录失败: {} - {}", file.getAbsolutePath(), e.getMessage());
                    result.setFailedFiles(result.getFailedFiles() + 1);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 重建单个文件记录
     */
    private boolean rebuildFileRecord(File file) throws IOException {
        // 计算文件MD5
        String md5 = FileUtils.calculateFileMD5(file);
        
        // 生成ULID作为记录ID
        String recordId = UlidUtils.generateUlid();
        
        // 创建文件传输记录
        FileTransferRecord record = new FileTransferRecord();
        record.setId(recordId);
        record.setFileId(md5);
        record.setFileName(file.getName());
        record.setFileSize(file.length());
        record.setFilePath(file.getAbsolutePath());
        record.setTransferredSize(file.length());
        record.setTotalChunks(1);
        record.setCompletedChunks(1);
        record.setStatus(2); // 传输完成
        record.setCreateTime(LocalDateTime.now().toString());
        record.setUpdateTime(LocalDateTime.now().toString());
        record.setCompleteTime(LocalDateTime.now().toString());
        
        // 插入数据库
        transferRecordMapper.insert(record);
        
        log.debug("重建文件记录: {} -> {}", file.getAbsolutePath(), md5);
        return true;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 格式化时间
     */
    private String formatTime(long timestamp) {
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.systemDefault()
        ).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 数据库健康信息
     */
    public static class DatabaseHealthInfo {
        private boolean connectionAvailable;
        private boolean databaseFileExists;
        private boolean tablesAccessible;
        private String databasePath;
        private long databaseFileSize;
        private long totalRecords;
        private String checkTime;
        private String errorMessage;
        
        // Getters and Setters
        public boolean isConnectionAvailable() { return connectionAvailable; }
        public void setConnectionAvailable(boolean connectionAvailable) { this.connectionAvailable = connectionAvailable; }
        
        public boolean isDatabaseFileExists() { return databaseFileExists; }
        public void setDatabaseFileExists(boolean databaseFileExists) { this.databaseFileExists = databaseFileExists; }
        
        public boolean isTablesAccessible() { return tablesAccessible; }
        public void setTablesAccessible(boolean tablesAccessible) { this.tablesAccessible = tablesAccessible; }
        
        public String getDatabasePath() { return databasePath; }
        public void setDatabasePath(String databasePath) { this.databasePath = databasePath; }
        
        public long getDatabaseFileSize() { return databaseFileSize; }
        public void setDatabaseFileSize(long databaseFileSize) { this.databaseFileSize = databaseFileSize; }
        
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        
        public String getCheckTime() { return checkTime; }
        public void setCheckTime(String checkTime) { this.checkTime = checkTime; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 备份文件信息
     */
    public static class BackupFileInfo {
        private String fileName;
        private String filePath;
        private long fileSize;
        private long createTime;
        private String formattedSize;
        private String formattedTime;
        private String fileDetails;
        
        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
        
        public String getFormattedSize() { return formattedSize; }
        public void setFormattedSize(String formattedSize) { this.formattedSize = formattedSize; }
        
        public String getFormattedTime() { return formattedTime; }
        public void setFormattedTime(String formattedTime) { this.formattedTime = formattedTime; }
        
        public String getFileDetails() { return fileDetails; }
        public void setFileDetails(String fileDetails) { this.fileDetails = fileDetails; }
    }
    
    /**
     * 重建操作信息
     */
    public static class RebuildOperationInfo {
        private final String operationId;
        private final String startTime;
        private final String initiatorUser;
        private final String initiatorIp;

        public RebuildOperationInfo(String operationId, String startTime, String initiatorUser, String initiatorIp) {
            this.operationId = operationId;
            this.startTime = startTime;
            this.initiatorUser = initiatorUser;
            this.initiatorIp = initiatorIp;
        }

        public String getOperationId() { return operationId; }
        public String getStartTime() { return startTime; }
        public String getInitiatorUser() { return initiatorUser; }
        public String getInitiatorIp() { return initiatorIp; }

        @Override
        public String toString() {
            return String.format("RebuildOperation{id=%s, startTime=%s, user=%s, ip=%s}",
                               operationId, startTime, initiatorUser, initiatorIp);
        }
    }

    /**
     * 数据库重建结果
     */
    public static class DatabaseRebuildResult {
        private boolean success;
        private String startTime;
        private String endTime;
        private int scannedFiles;
        private int rebuiltRecords;
        private int failedFiles;
        private String errorMessage;
        private String message;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        
        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }
        
        public int getScannedFiles() { return scannedFiles; }
        public void setScannedFiles(int scannedFiles) { this.scannedFiles = scannedFiles; }
        
        public int getRebuiltRecords() { return rebuiltRecords; }
        public void setRebuiltRecords(int rebuiltRecords) { this.rebuiltRecords = rebuiltRecords; }
        
        public int getFailedFiles() { return failedFiles; }
        public void setFailedFiles(int failedFiles) { this.failedFiles = failedFiles; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
