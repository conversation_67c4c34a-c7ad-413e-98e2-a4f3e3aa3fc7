package com.sdesrd.filetransfer.client.util;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 重试管理器测试
 */
@Slf4j
@DisplayName("重试管理器测试")
class RetryManagerTest {
    
    @Test
    @DisplayName("成功执行操作")
    void testSuccessfulOperation() throws Exception {
        String result = RetryManager.executeWithRetry(
                () -> "success",
                new RetryManager.RetryPolicyBuilder()
                        .maxRetries(3)
                        .baseDelay(100L)
                        .maxDelay(1000L)
                        .backoffMultiplier(2.0)
                        .jitterFactor(0.1)
                        .build(),
                "测试操作"
        );
        
        assertEquals("success", result);
    }
    
    @Test
    @DisplayName("重试后成功")
    void testRetryThenSuccess() throws Exception {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int attempt = attemptCount.incrementAndGet();
                    if (attempt <= 2) {
                        throw new RuntimeException("模拟失败 - 尝试次数: " + attempt);
                    }
                    return "success after " + attempt + " attempts";
                },
                new RetryManager.RetryPolicyBuilder()
                        .maxRetries(3)
                        .baseDelay(50L)
                        .maxDelay(200L)
                        .backoffMultiplier(2.0)
                        .jitterFactor(0.1)
                        .build(),
                "重试测试"
        );
        
        assertEquals("success after 3 attempts", result);
        assertEquals(3, attemptCount.get());
    }
    
    @Test
    @DisplayName("重试次数用尽后失败")
    void testFailAfterMaxRetries() {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        int attempt = attemptCount.incrementAndGet();
                        throw new RuntimeException("始终失败 - 尝试次数: " + attempt);
                    },
                    new RetryManager.RetryPolicyBuilder()
                            .maxRetries(2)
                            .baseDelay(10L)
                            .maxDelay(50L)
                            .backoffMultiplier(2.0)
                            .jitterFactor(0.1)
                            .build(),
                    "失败测试"
            );
        });
        
        assertTrue(exception.getMessage().contains("重试失败"));
        assertEquals(3, attemptCount.get()); // 1次初始尝试 + 2次重试
    }
    
    @Test
    @DisplayName("测试使用默认策略的便捷方法")
    void testDefaultPolicyConvenience() throws Exception {
        String result = RetryManager.executeWithRetry(
                () -> "default success",
                "默认策略测试"
        );
        
        assertEquals("default success", result);
    }
    
    @Test
    @DisplayName("测试Runnable操作重试")
    void testRunnableRetry() throws Exception {
        AtomicInteger counter = new AtomicInteger(0);
        
        RetryManager.executeWithRetry(
                () -> {
                    counter.incrementAndGet();
                },
                new RetryManager.RetryPolicyBuilder()
                        .maxRetries(1)
                        .baseDelay(10L)
                        .maxDelay(50L)
                        .backoffMultiplier(2.0)
                        .jitterFactor(0.1)
                        .build(),
                "Runnable测试"
        );
        
        assertEquals(1, counter.get());
    }
    
    @Test
    @DisplayName("测试延迟计算")
    void testDelayCalculation() {
        RetryManager.RetryPolicy policy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(3)
                .baseDelay(100L)
                .maxDelay(1000L)
                .backoffMultiplier(2.0)
                .jitterFactor(0.0) // 无抖动，便于测试
                .build();
        
        assertEquals(3, policy.getMaxRetries());
        assertEquals(100L, policy.getBaseDelayMs());
        assertEquals(1000L, policy.getMaxDelayMs());
        assertEquals(2.0, policy.getBackoffMultiplier());
        assertEquals(0.0, policy.getJitterFactor());
    }
    
    @Test
    @DisplayName("测试构建器模式")
    void testBuilderPattern() {
        RetryManager.RetryPolicy policy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(5)
                .baseDelay(200L)
                .maxDelay(5000L)
                .backoffMultiplier(1.5)
                .jitterFactor(0.2)
                .build();
        
        assertEquals(5, policy.getMaxRetries());
        assertEquals(200L, policy.getBaseDelayMs());
        assertEquals(5000L, policy.getMaxDelayMs());
        assertEquals(1.5, policy.getBackoffMultiplier());
        assertEquals(0.2, policy.getJitterFactor());
    }
    

    
    @Test
    @DisplayName("测试自定义重试策略")
    void testCustomRetryPolicy() throws Exception {
        log.info("[测试开始] 测试自定义重试策略");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        // 创建自定义重试策略：最多重试1次，基础延迟50ms
        RetryManager.RetryPolicy customPolicy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(1)
                .baseDelay(50L)
                .maxDelay(200L)
                .backoffMultiplier(2.0)
                .jitterFactor(0.0) // 无抖动，便于测试
                .build();
        
        long startTime = System.currentTimeMillis();
        
        String result = RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 2) {
                        throw new RuntimeException("模拟失败");
                    }
                    return "自定义重试成功";
                },
                customPolicy,
                "测试自定义重试"
        );
        
        long duration = System.currentTimeMillis() - startTime;
        
        assertEquals("自定义重试成功", result, "应该返回正确结果");
        assertEquals(2, callCount.get(), "应该调用2次");
        assertTrue(duration >= 50, "应该有延迟时间，实际耗时: " + duration + "ms");
        assertTrue(duration < 500, "延迟时间应该合理，实际耗时: " + duration + "ms");
        
        log.info("[测试完成] 自定义重试策略测试通过 - 耗时: {}ms", duration);
    }
    
    @Test
    @DisplayName("测试可重试异常过滤")
    void testRetryableExceptionFilter() {
        log.info("[测试开始] 测试可重试异常过滤");
        
        AtomicInteger callCount = new AtomicInteger(0);
        
        // 创建只重试RuntimeException的策略（因为我们包装了IOException）
        RetryManager.RetryPolicy policy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(2)
                .baseDelay(10L)
                .retryOn(RuntimeException.class)
                .build();

        // 测试可重试异常
        log.info("[测试步骤] 测试可重试异常（RuntimeException包装IOException）");
        callCount.set(0);

        Exception ioException = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        callCount.incrementAndGet();
                        throw new RuntimeException(new IOException("网络异常"));
                    },
                    policy,
                    "测试IO异常"
            );
        });

        assertEquals(3, callCount.get(), "RuntimeException应该重试，总共调用3次");
        
        // 测试不可重试异常（使用不同的策略）
        log.info("[测试步骤] 测试不可重试异常（IllegalArgumentException）");
        callCount.set(0);

        // 创建只重试IOException的策略
        RetryManager.RetryPolicy ioOnlyPolicy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(2)
                .baseDelay(10L)
                .retryOn(IOException.class)
                .build();

        Exception illegalException = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        callCount.incrementAndGet();
                        throw new IllegalArgumentException("参数异常");
                    },
                    ioOnlyPolicy,
                    "测试参数异常"
            );
        });

        assertEquals(1, callCount.get(), "IllegalArgumentException不应该重试，只调用1次");
        
        log.info("[测试完成] 可重试异常过滤测试通过");
    }
    
    @Test
    @DisplayName("测试无返回值操作重试")
    void testVoidOperationRetry() throws Exception {
        log.info("[测试开始] 测试无返回值操作重试");
        
        AtomicInteger callCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        RetryManager.executeWithRetry(
                () -> {
                    int count = callCount.incrementAndGet();
                    log.info("执行第{}次调用", count);
                    
                    if (count < 3) {
                        throw new RuntimeException("模拟失败");
                    }
                    
                    successCount.incrementAndGet();
                    log.info("操作成功执行");
                },
                new RetryManager.RetryPolicyBuilder()
                        .maxRetries(3)
                        .baseDelay(100L)
                        .maxDelay(1000L)
                        .backoffMultiplier(2.0)
                        .jitterFactor(0.1)
                        .build(),
                "测试无返回值操作"
        );
        
        assertEquals(3, callCount.get(), "应该调用3次");
        assertEquals(1, successCount.get(), "应该成功执行1次");
        
        log.info("[测试完成] 无返回值操作重试测试通过");
    }
    
    @Test
    @DisplayName("测试指数退避延迟计算")
    void testExponentialBackoffDelay() throws Exception {
        log.info("[测试开始] 测试指数退避延迟计算");
        
        AtomicInteger callCount = new AtomicInteger(0);
        long[] callTimes = new long[4];
        
        // 创建有明确延迟的重试策略
        RetryManager.RetryPolicy policy = new RetryManager.RetryPolicyBuilder()
                .maxRetries(3)
                .baseDelay(100L)
                .backoffMultiplier(2.0)
                .jitterFactor(0.0) // 无抖动，便于测试
                .build();
        
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RetryManager.executeWithRetry(
                    () -> {
                        int count = callCount.incrementAndGet();
                        callTimes[count - 1] = System.currentTimeMillis();
                        log.info("执行第{}次调用 - 时间: {}", count, callTimes[count - 1]);
                        throw new RuntimeException("持续失败");
                    },
                    policy,
                    "测试指数退避"
            );
        });
        
        assertEquals(4, callCount.get(), "应该调用4次");
        
        // 验证延迟时间（允许一定误差）
        long delay1 = callTimes[1] - callTimes[0]; // 第一次重试延迟
        long delay2 = callTimes[2] - callTimes[1]; // 第二次重试延迟
        long delay3 = callTimes[3] - callTimes[2]; // 第三次重试延迟
        
        log.info("延迟时间 - 第1次重试: {}ms, 第2次重试: {}ms, 第3次重试: {}ms", 
                delay1, delay2, delay3);
        
        // 验证指数退避（允许±50ms误差）
        assertTrue(Math.abs(delay1 - 100) <= 50, "第1次重试延迟应该约为100ms，实际: " + delay1 + "ms");
        assertTrue(Math.abs(delay2 - 200) <= 50, "第2次重试延迟应该约为200ms，实际: " + delay2 + "ms");
        assertTrue(Math.abs(delay3 - 400) <= 50, "第3次重试延迟应该约为400ms，实际: " + delay3 + "ms");
        
        log.info("[测试完成] 指数退避延迟计算测试通过");
    }
}
