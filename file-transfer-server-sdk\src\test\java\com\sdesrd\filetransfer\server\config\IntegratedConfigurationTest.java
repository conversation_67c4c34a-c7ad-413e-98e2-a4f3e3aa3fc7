package com.sdesrd.filetransfer.server.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.server.util.PerformanceMonitor;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 集成配置测试
 * 验证所有配置修复后的整体协调工作
 */
@Slf4j
@DisplayName("集成配置测试")
class IntegratedConfigurationTest {
    
    private FileTransferProperties properties;
    
    @BeforeEach
    void setUp() {
        properties = new FileTransferProperties();
        log.info("集成配置测试 - 开始验证所有配置项的协调工作");
        
        // 停止所有可能运行的定时任务
        RateLimitUtils.stopCleanupScheduler();
    }
    
    @AfterEach
    void tearDown() {
        // 清理资源
        RateLimitUtils.stopCleanupScheduler();
        RateLimitUtils.clearAllRateLimiters();
        log.info("集成配置测试 - 清理完成");
    }
    
    @Test
    @DisplayName("验证所有新增配置项都有合理的默认值")
    void testAllNewConfigurationDefaults() {
        log.info("验证所有新增配置项的默认值");
        
        // API签名配置
        assertEquals(300L, properties.getApiSignatureTtlSeconds(), 
            "API签名时效默认值应该为300秒");
        
        // 数据库备份配置
        assertEquals(24, properties.getDatabaseBackupIntervalHours(), 
            "数据库备份间隔默认值应该为24小时");
        assertEquals(7, properties.getDatabaseBackupRetentionDays(), 
            "数据库备份保留期默认值应该为7天");
        
        // 数据库健康检查配置
        assertEquals(5000L, properties.getDatabaseHealthCheckTimeoutMs(), 
            "数据库健康检查超时默认值应该为5000ms");
        assertEquals(30000L, properties.getDatabaseHealthCheckIntervalMs(), 
            "数据库健康检查间隔默认值应该为30000ms");
        
        // 性能监控配置
        assertEquals(30, properties.getPerformanceMonitorIntervalSeconds(), 
            "性能监控间隔默认值应该为30秒");
        assertEquals(3600000L, properties.getPerformanceMetricsRetentionMs(), 
            "性能指标保留时间默认值应该为3600000ms");
        
        // 限流器配置
        assertEquals(300000L, properties.getRateLimiterCleanupIntervalMs(), 
            "限流器清理间隔默认值应该为300000ms");
        assertEquals(600000L, properties.getRateLimiterIdleTimeoutMs(), 
            "限流器空闲超时默认值应该为600000ms");
        
        log.info("✓ 所有新增配置项默认值验证通过");
    }
    
    @Test
    @DisplayName("验证配置项之间的时间关系合理")
    void testConfigurationTimeRelationships() {
        log.info("验证配置项之间的时间关系");
        
        // API签名时效应该远小于传输会话过期时间
        assertTrue(properties.getApiSignatureTtlSeconds() * 1000 < properties.getTokenExpire(),
            "API签名时效应该小于传输会话过期时间");
        
        // 数据库健康检查超时应该小于检查间隔
        assertTrue(properties.getDatabaseHealthCheckTimeoutMs() < properties.getDatabaseHealthCheckIntervalMs(),
            "数据库健康检查超时应该小于检查间隔");
        
        // 限流器空闲超时应该大于清理间隔，避免频繁清理
        assertTrue(properties.getRateLimiterIdleTimeoutMs() > properties.getRateLimiterCleanupIntervalMs(),
            "限流器空闲超时应该大于清理间隔");
        
        // 性能指标保留时间应该大于监控间隔
        assertTrue(properties.getPerformanceMetricsRetentionMs() > properties.getPerformanceMonitorIntervalSeconds() * 1000,
            "性能指标保留时间应该大于监控间隔");
        
        log.info("✓ 配置项时间关系验证通过");
    }
    
    @Test
    @DisplayName("验证配置系统的完整初始化流程")
    void testCompleteConfigurationInitialization() {
        log.info("验证配置系统完整初始化流程");
        
        // 设置自定义配置值
        properties.setApiSignatureTtlSeconds(600L);
        properties.setDatabaseBackupIntervalHours(48);
        properties.setDatabaseBackupRetentionDays(14);
        properties.setDatabaseHealthCheckTimeoutMs(10000L);
        properties.setDatabaseHealthCheckIntervalMs(60000L);
        properties.setPerformanceMonitorIntervalSeconds(60);
        properties.setPerformanceMetricsRetentionMs(7200000L);
        properties.setRateLimiterCleanupIntervalMs(600000L);
        properties.setRateLimiterIdleTimeoutMs(1200000L);
        
        // 模拟FileTransferAutoConfiguration的初始化过程
        
        // 1. 配置性能监控器
        PerformanceMonitor.configure(
            properties.getPerformanceMonitorIntervalSeconds(),
            properties.getPerformanceMetricsRetentionMs()
        );
        
        // 2. 配置限流器
        RateLimitUtils.configure(
            properties.getRateLimiterCleanupIntervalMs(),
            properties.getRateLimiterIdleTimeoutMs()
        );
        
        // 验证配置生效
        assertTrue(RateLimitUtils.isCleanupRunning(), "限流器清理任务应该启动");
        
        String rateLimiterConfig = RateLimitUtils.getConfigurationInfo();
        assertTrue(rateLimiterConfig.contains("600000ms"), "限流器配置应该包含正确的清理间隔");
        assertTrue(rateLimiterConfig.contains("1200000ms"), "限流器配置应该包含正确的空闲超时");
        assertTrue(rateLimiterConfig.contains("运行中"), "限流器清理任务应该运行中");
        
        log.info("配置初始化完成 - 限流器配置: {}", rateLimiterConfig);
        log.info("✓ 配置系统完整初始化流程验证通过");
    }
    
    @Test
    @DisplayName("验证配置修复前后的兼容性")
    void testBackwardCompatibilityAfterFix() {
        log.info("验证配置修复前后的兼容性");
        
        // 验证原有配置项未受影响
        assertTrue(properties.isEnabled(), "服务启用状态应该保持默认值");
        assertEquals("./data/file-transfer/database.db", properties.getDatabasePath(), 
            "数据库路径应该保持默认值");
        assertEquals(3600000L, properties.getTokenExpire(), 
            "传输会话过期时间应该保持默认值");
        assertTrue(properties.isCleanupEnabled(), "清理功能应该保持默认启用状态");
        assertEquals(3600000L, properties.getCleanupInterval(), 
            "清理间隔应该保持默认值");
        assertEquals(86400000L, properties.getRecordExpireTime(), 
            "记录过期时间应该保持默认值");
        
        // 验证新增配置项不会影响现有功能
        FileTransferProperties defaultProps = new FileTransferProperties();
        assertEquals(properties.isEnabled(), defaultProps.isEnabled());
        assertEquals(properties.getDatabasePath(), defaultProps.getDatabasePath());
        assertEquals(properties.getTokenExpire(), defaultProps.getTokenExpire());
        assertEquals(properties.isCleanupEnabled(), defaultProps.isCleanupEnabled());
        
        log.info("✓ 配置修复前后兼容性验证通过");
    }
    
    @Test
    @DisplayName("验证配置项的边界值处理")
    void testConfigurationBoundaryValues() {
        log.info("验证配置项边界值处理");
        
        // 测试最小合理值
        properties.setApiSignatureTtlSeconds(60L); // 1分钟
        properties.setDatabaseBackupIntervalHours(1); // 1小时
        properties.setDatabaseBackupRetentionDays(1); // 1天
        properties.setDatabaseHealthCheckTimeoutMs(1000L); // 1秒
        properties.setDatabaseHealthCheckIntervalMs(5000L); // 5秒
        properties.setPerformanceMonitorIntervalSeconds(10); // 10秒
        properties.setPerformanceMetricsRetentionMs(60000L); // 1分钟
        properties.setRateLimiterCleanupIntervalMs(60000L); // 1分钟
        properties.setRateLimiterIdleTimeoutMs(120000L); // 2分钟
        
        // 验证设置成功
        assertEquals(60L, properties.getApiSignatureTtlSeconds());
        assertEquals(1, properties.getDatabaseBackupIntervalHours());
        assertEquals(1, properties.getDatabaseBackupRetentionDays());
        assertEquals(1000L, properties.getDatabaseHealthCheckTimeoutMs());
        assertEquals(5000L, properties.getDatabaseHealthCheckIntervalMs());
        assertEquals(10, properties.getPerformanceMonitorIntervalSeconds());
        assertEquals(60000L, properties.getPerformanceMetricsRetentionMs());
        assertEquals(60000L, properties.getRateLimiterCleanupIntervalMs());
        assertEquals(120000L, properties.getRateLimiterIdleTimeoutMs());
        
        // 测试配置能够正常工作
        RateLimitUtils.configure(
            properties.getRateLimiterCleanupIntervalMs(),
            properties.getRateLimiterIdleTimeoutMs()
        );
        
        assertTrue(RateLimitUtils.isCleanupRunning(), "即使使用边界值，限流器清理任务也应该正常启动");
        
        log.info("✓ 配置项边界值处理验证通过");
    }
    
    @Test
    @DisplayName("验证配置系统的资源管理")
    void testConfigurationResourceManagement() {
        log.info("验证配置系统资源管理");
        
        // 启动配置
        RateLimitUtils.configure(1000L, 500L);
        assertTrue(RateLimitUtils.isCleanupRunning(), "配置后清理任务应该启动");
        
        // 创建一些限流器
        RateLimitUtils.applyRateLimit("test1", 1024L, 100L);
        RateLimitUtils.applyRateLimit("test2", 1024L, 100L);
        assertEquals(2, RateLimitUtils.getRateLimiterCount(), "应该创建2个限流器");
        
        // 停止清理任务
        RateLimitUtils.stopCleanupScheduler();
        assertFalse(RateLimitUtils.isCleanupRunning(), "停止后清理任务应该停止");
        
        // 清理所有限流器
        RateLimitUtils.clearAllRateLimiters();
        assertEquals(0, RateLimitUtils.getRateLimiterCount(), "清理后应该没有限流器");
        
        log.info("✓ 配置系统资源管理验证通过");
    }
}
