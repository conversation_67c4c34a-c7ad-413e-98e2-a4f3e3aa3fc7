<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sdesrd.filetransfer</groupId>
        <artifactId>file-transfer-sdk-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>file-transfer-client-demo</artifactId>
    <packaging>jar</packaging>
    <name>文件传输客户端演示应用</name>
    <description>文件传输客户端SDK演示应用，展示端到端测试功能</description>

    <properties>
        <!-- Java 8字节码编译配置 -->
        <java.target.version>8</java.target.version>
        <java.release.version>8</java.release.version>

        <!-- 演示应用主类 -->
        <main.class>com.sdesrd.filetransfer.demo.FileTransferClientDemo</main.class>
        
        <!-- 演示文件目录常量 -->
        <demo.upload.dir>demo-files/upload</demo.upload.dir>
        <demo.download.dir>demo-files/download</demo.download.dir>
        
        <!-- 服务器连接配置常量 -->
        <demo.server.host>localhost</demo.server.host>
        <demo.server.port>49011</demo.server.port>
        <demo.user.name>demo</demo.user.name>
        <demo.user.secret>demo-secret-key-2024</demo.user.secret>
        
        <!-- 传输配置常量 -->
        <demo.chunk.size>1048576</demo.chunk.size> <!-- 1MB -->
        <demo.max.concurrent.transfers>3</demo.max.concurrent.transfers>
        <demo.retry.count>3</demo.retry.count>
        <demo.retry.delay>1000</demo.retry.delay>
        
        <!-- 测试文件大小常量 -->
        <demo.small.file.size>10240</demo.small.file.size> <!-- 10KB -->
        <demo.medium.file.size>1048576</demo.medium.file.size> <!-- 1MB -->
        <demo.large.file.size>10485760</demo.large.file.size> <!-- 10MB -->
    </properties>

    <dependencies>
        <!-- 文件传输客户端SDK -->
        <dependency>
            <groupId>com.sdesrd.filetransfer</groupId>
            <artifactId>file-transfer-client-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Apache Commons IO -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!-- Apache Commons Lang -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- Hutool工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <!-- ULID支持 -->
        <dependency>
            <groupId>com.github.f4b6a3</groupId>
            <artifactId>ulid-creator</artifactId>
        </dependency>

        <!-- 日志依赖 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.target.version}</source>
                    <target>${java.target.version}</target>
                    <encoding>UTF-8</encoding>
                    <release>${java.release.version}</release>
                    <compilerArgs>
                        <arg>--release</arg>
                        <arg>${java.release.version}</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- Maven资源插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- Maven执行插件 - 用于运行演示应用 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>${main.class}</mainClass>
                    <cleanupDaemonThreads>false</cleanupDaemonThreads>
                    <systemProperties>
                        <!-- 演示配置系统属性 -->
                        <systemProperty>
                            <key>demo.upload.dir</key>
                            <value>${demo.upload.dir}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.download.dir</key>
                            <value>${demo.download.dir}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.server.host</key>
                            <value>${demo.server.host}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.server.port</key>
                            <value>${demo.server.port}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.user.name</key>
                            <value>${demo.user.name}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.user.secret</key>
                            <value>${demo.user.secret}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.chunk.size</key>
                            <value>${demo.chunk.size}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.max.concurrent.transfers</key>
                            <value>${demo.max.concurrent.transfers}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.retry.count</key>
                            <value>${demo.retry.count}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.retry.delay</key>
                            <value>${demo.retry.delay}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.small.file.size</key>
                            <value>${demo.small.file.size}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.medium.file.size</key>
                            <value>${demo.medium.file.size}</value>
                        </systemProperty>
                        <systemProperty>
                            <key>demo.large.file.size</key>
                            <value>${demo.large.file.size}</value>
                        </systemProperty>
                    </systemProperties>
                </configuration>
            </plugin>

            <!-- Maven JAR插件 - 创建可执行JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>${main.class}</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Implementation-Title>${project.name}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Vendor>SDESRD</Implementation-Vendor>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <!-- Maven依赖插件 - 复制依赖到lib目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven源码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Javadoc插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <charset>UTF-8</charset>
                    <docencoding>UTF-8</docencoding>
                    <doclint>none</doclint>
                    <quiet>true</quiet>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Surefire Plugin for testing -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 配置文件 -->
    <profiles>


        
        <!-- 开发环境配置 -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <demo.server.host>localhost</demo.server.host>
                <demo.server.port>49011</demo.server.port>
            </properties>
        </profile>

        <!-- 测试环境配置 -->
        <profile>
            <id>test</id>
            <properties>
                <demo.server.host>localhost</demo.server.host>
                <demo.server.port>49011</demo.server.port>
            </properties>
        </profile>

        <!-- 生产环境配置 -->
        <profile>
            <id>prod</id>
            <properties>
                <demo.server.host>your-server-host</demo.server.host>
                <demo.server.port>49011</demo.server.port>
            </properties>
        </profile>
    </profiles>
</project>
